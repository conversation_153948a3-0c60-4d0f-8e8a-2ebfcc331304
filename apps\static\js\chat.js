// chat.js - 处理聊天交互

// 定义全局变量以供所有函数访问
let chatContainer; // 聊天容器引用

document.addEventListener('DOMContentLoaded', function() {
    // 聊天表单元素
    const chatForm = document.getElementById('chat-form');
    const messageInput = document.getElementById('messageInput');
    const modelSelect = document.getElementById('selectedModel');
    const apiKeyInput = document.getElementById('currentApiKey');
    chatContainer = document.getElementById('chatContainer'); // 赋值给全局变量
    const streamToggle = document.getElementById('streamToggle');
    
    // 检查所有必要的DOM元素是否存在
    const missingElements = [];
    if (!chatForm) missingElements.push('chat-form');
    if (!messageInput) missingElements.push('messageInput');
    if (!modelSelect) missingElements.push('selectedModel');
    if (!chatContainer) missingElements.push('chatContainer');
    
    if (missingElements.length > 0) {
        console.error('聊天功能初始化失败：缺少必要的DOM元素：', missingElements);
    }
    
    // 如果聊天表单存在，才添加事件监听
    if (chatForm) {
        // 发送聊天消息
        chatForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // 再次检查关键元素
            if (!messageInput || !modelSelect || !chatContainer) {
                console.error('发送消息失败：缺少必要的DOM元素');
                return;
            }
            
            // 获取输入
            const message = messageInput.value.trim();
            const model = modelSelect.value;
            // 安全地获取API密钥，如果元素不存在则使用空字符串
            const apiKey = apiKeyInput ? apiKeyInput.value.trim() : '';
            
            // 安全地获取选中的MCP服务器
            const mcpServers = [];
            const checkboxes = document.querySelectorAll('#mcpServerCheckboxes input[type="checkbox"]:checked');
            if (checkboxes) {
                checkboxes.forEach(checkbox => {
                    mcpServers.push(checkbox.value);
                });
            }
            
            // 检查消息是否为空
            if (!message) {
                alert('请输入消息');
                return;
            }
            
            // 检查是否选择了模型
            if (!model) {
                alert('请选择模型');
                return;
            }
            
            // 添加用户消息到聊天界面
            appendMessage('user', message);
            
            // 清空输入框
            messageInput.value = '';
            
            // 显示处理中状态
            const assistantMessage = document.createElement('div');
            assistantMessage.className = 'message assistant-message';
            
            const assistantContent = document.createElement('div');
            assistantContent.className = 'message-content';
            assistantContent.id = 'assistant-response-' + Date.now(); // 唯一ID
            assistantContent.innerHTML = '<div class="typing-indicator"><span></span><span></span><span></span></div>';
            
            assistantMessage.appendChild(assistantContent);
            chatContainer.appendChild(assistantMessage);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            
            try {
                // 根据是否启用流式输出选择不同的处理方法
                if (streamToggle && streamToggle.checked) {
                    // 流式输出处理
                    await handleStreamResponse(model, apiKey, message, mcpServers, assistantContent);
                } else {
                    // 标准输出处理
                    await handleStandardResponse(model, apiKey, message, mcpServers, assistantContent);
                }
            } catch (error) {
                console.error('处理聊天请求时出错:', error);
                if (assistantContent) {
                    assistantContent.innerHTML = `<div class="error">系统错误，请稍后重试</div>`;
                }
            }
        });
    }
    
    // 处理标准响应
    async function handleStandardResponse(model, apiKey, message, mcpServers, assistantContent) {
        try {
            if (!assistantContent) {
                console.error('处理响应失败：assistantContent元素不存在');
                return;
            }
            
            // 发送请求
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    model: model,
                    api_key: apiKey,
                    mcp_servers: mcpServers,
                    stream: false,
                    session_id: 'default' // 添加会话ID
                })
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || '请求失败');
            }
            
            const data = await response.json();
            assistantContent.innerHTML = formatMessageContent(data.response);
            
            // 安全地滚动到底部
            if (chatContainer) {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
            
        } catch (error) {
            if (assistantContent) {
                assistantContent.innerHTML = `<div class="error">错误: ${error.message}</div>`;
            }
            console.error('Chat error:', error);
        }
    }
    
    // 处理流式响应
    async function handleStreamResponse(model, apiKey, message, mcpServers, assistantContent) {
        try {
            if (!assistantContent) {
                console.error('处理流式响应失败：assistantContent元素不存在');
                return;
            }
            
            // 清除打字指示器
            assistantContent.innerHTML = '';
            
            // 构建查询参数
            const queryParams = new URLSearchParams({
                message: message,
                model: model,
                stream: 'true',
                session_id: 'default' // 添加会话ID
            });
            
            // 添加可选参数
            if (apiKey) {
                queryParams.append('api_key', apiKey);
            }
            
            if (mcpServers && mcpServers.length > 0) {
                queryParams.append('mcp_servers', mcpServers.join(','));
            }
            
            // 完整URL
            const streamUrl = `/api/chat?${queryParams.toString()}`;
            console.log("建立SSE连接:", streamUrl);
            
            // 创建事件源
            const eventSource = new EventSource(streamUrl);
            
            // 变量用于累积完整响应
            let fullResponse = '';
            let eventCount = 0;
            
            // 处理连接建立
            eventSource.onopen = function() {
                console.log("SSE连接已打开");
                if (assistantContent) {
                    assistantContent.innerHTML = '<div class="typing-indicator"><span></span><span></span><span></span></div>';
                }
            };
            
            // 处理服务器发送的事件
            eventSource.onmessage = function(event) {
                try {
                    eventCount++;
                    console.log(`收到SSE事件 #${eventCount}:`, event.data);
                    
                    const data = JSON.parse(event.data);
                    
                    if (data.done) {
                        // 流结束
                        console.log("流结束，关闭SSE连接");
                        eventSource.close();
                        
                        // 确保最后更新一次内容
                        if (fullResponse && assistantContent) {
                            assistantContent.innerHTML = formatMessageContent(fullResponse);
                            // 安全地滚动到底部
                            if (chatContainer) {
                                chatContainer.scrollTop = chatContainer.scrollHeight;
                            }
                        }
                    } else {
                        // 添加新内容
                        if (data.content && assistantContent) {
                            // 清除打字指示器
                            if (eventCount === 1) {
                                assistantContent.innerHTML = '';
                            }
                            
                            fullResponse += data.content;
                            assistantContent.innerHTML = formatMessageContent(fullResponse);
                            // 安全地滚动到底部
                            if (chatContainer) {
                                chatContainer.scrollTop = chatContainer.scrollHeight;
                            }
                        }
                    }
                } catch (e) {
                    console.error('解析事件数据失败:', e, event.data);
                }
            };
            
            // 处理错误
            eventSource.onerror = function(error) {
                console.error('SSE错误:', error);
                eventSource.close();
                
                // 如果有内容但出错，保留已有内容；如果没有内容则显示错误信息
                if (!fullResponse && assistantContent) {
                    assistantContent.innerHTML = '<div class="error">流式响应失败</div>';
                }
            };
            
        } catch (error) {
            if (assistantContent) {
                assistantContent.innerHTML = `<div class="error">错误: ${error.message}</div>`;
            }
            console.error('Stream error:', error);
        }
    }
    
    // 格式化消息内容(支持Markdown和代码高亮)
    function formatMessageContent(content) {
        // 如果需要Markdown支持，可以在这里添加
        return content
            .replace(/\n/g, '<br>')
            .replace(/```(\w+)?\n([\s\S]*?)\n```/g, '<pre><code class="$1">$2</code></pre>');
    }
    
    // 添加消息到聊天界面
    function appendMessage(role, content) {
        if (!chatContainer) {
            console.error('添加消息失败：聊天容器不存在');
            return;
        }
        
        try {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            if (role === 'user') {
                contentDiv.innerText = content;
            } else {
                contentDiv.innerHTML = formatMessageContent(content);
            }
            
            messageDiv.appendChild(contentDiv);
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        } catch (error) {
            console.error('添加消息到聊天界面时出错:', error);
        }
    }
}); 