"""
Simple SSE (Server-Sent Events) client for MCP servers
"""
import time
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class SSEMCPClient:
    """Client for Server-Sent Events (SSE) MCP endpoints"""
    
    def __init__(self, url: str, timeout: int = 30):
        """Initialize with SSE endpoint URL"""
        self.url = url
        self.timeout = timeout
        
    def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the SSE endpoint"""
        start_time = time.time()
        
        try:
            import requests
            
            # Use custom headers for SSE connection
            headers = {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            }
            
            # Try to connect to the SSE endpoint
            response = requests.get(
                self.url, 
                headers=headers, 
                stream=True, 
                timeout=self.timeout
            )
            
            response_time = round((time.time() - start_time) * 1000, 1)
            
            # Check if successful
            if response.status_code == 200 and 'text/event-stream' in response.headers.get('content-type', ''):
                return {
                    'status': 'healthy',
                    'message': 'SSE connection successful',
                    'response_time': response_time,
                    'content_type': response.headers.get('content-type', ''),
                    'server': response.headers.get('server', '')
                }
            elif response.status_code == 200:
                return {
                    'status': 'warning',
                    'message': f'Wrong content type: {response.headers.get("content-type", "none")}',
                    'response_time': response_time
                }
            else:
                return {
                    'status': 'error',
                    'message': f'HTTP error: {response.status_code}',
                    'response_time': response_time
                }
        except ImportError:
            return {
                'status': 'error',
                'message': 'requests library not available',
                'response_time': None
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Connection error: {str(e)}',
                'response_time': round((time.time() - start_time) * 1000, 1) if start_time else None
            }
    
    def send_message(self, tool: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Send a message to the MCP server"""
        try:
            import requests
            
            # Convert SSE URL to message URL
            if self.url.endswith('/sse'):
                base_url = self.url[:-4]
            else:
                base_url = self.url
                
            message_url = f"{base_url}/message"
            
            # Send request
            response = requests.post(
                message_url,
                json={'tool': tool, 'params': params},
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'error': f'HTTP error: {response.status_code}',
                    'message': f'Failed to call tool {tool}'
                }
        except ImportError:
            return {
                'error': 'requests library not available',
                'message': f'Failed to call tool {tool}'
            }
        except Exception as e:
            return {
                'error': str(e),
                'message': f'Failed to call tool {tool}'
            }
    
    def get_tools(self) -> Optional[Dict[str, Any]]:
        """Get available tools from the MCP server"""
        try:
            import requests
            
            # Convert SSE URL to tools URL
            if self.url.endswith('/sse'):
                base_url = self.url[:-4]
            else:
                base_url = self.url
                
            tools_url = f"{base_url}/tools"
            
            # Send request
            response = requests.get(tools_url, timeout=self.timeout)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'error': f'HTTP error: {response.status_code}',
                    'message': 'Failed to get tools'
                }
        except ImportError:
            return {
                'error': 'requests library not available',
                'message': 'Failed to get tools'
            }
        except Exception as e:
            return {
                'error': str(e),
                'message': 'Failed to get tools'
            }
    
    def stream_events(self, callback=None):
        """Stream events from the SSE endpoint"""
        try:
            import requests
            
            headers = {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            }
            
            response = requests.get(
                self.url,
                headers=headers,
                stream=True,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                for line in response.iter_lines(decode_unicode=True):
                    if line:
                        # Parse SSE format
                        if line.startswith('data: '):
                            data = line[6:]  # Remove 'data: ' prefix
                            if callback:
                                callback(data)
                            else:
                                logger.info(f"SSE Event: {data}")
            else:
                logger.error(f"Failed to connect to SSE stream: {response.status_code}")
                
        except ImportError:
            logger.error("requests library not available for SSE streaming")
        except Exception as e:
            logger.error(f"Error streaming SSE events: {e}")


class StandardMCPClient:
    """Standard MCP client implementation using the official MCP library"""
    
    def __init__(self, server_name: str, server_config: Dict[str, Any]):
        """Initialize the standard MCP client"""
        self.server_name = server_name
        self.config = server_config
        self.connection_type = server_config.get("connection_type", "stdio")
        
        # Check if MCP libraries are available
        try:
            from mcp import ClientSession, StdioServerParameters, types
            from mcp.client.stdio import stdio_client
            from mcp.client.sse import sse_client
            self.mcp_available = True
        except ImportError:
            logger.warning("Standard MCP client libraries not available")
            self.mcp_available = False
    
    async def get_tools(self) -> List[Dict[str, Any]]:
        """Get the list of tools using the official MCP client library"""
        if not self.mcp_available:
            logger.error("Cannot get tools: MCP client library not available")
            return []
        
        try:
            from mcp.client.stdio import stdio_client
            from mcp.client.sse import sse_client
            from mcp import ClientSession
            
            if self.connection_type == "stdio":
                # Use stdio client
                server_params = self.config.get("server_params", {})
                async with stdio_client(server_params) as (read_stream, write_stream):
                    async with ClientSession(read_stream, write_stream) as session:
                        logger.info(f"Getting tools from stdio server {self.server_name}")
                        await session.initialize()
                        tools_result = await session.list_tools()
                        return [tool.dict() for tool in tools_result.tools]
            else:
                # Use SSE client
                sse_url = self.config.get("url", "")
                headers = self.config.get("headers", {})
                
                if "Accept" not in headers:
                    headers["Accept"] = "text/event-stream"
                    
                async with sse_client(sse_url, headers=headers) as (read_stream, write_stream):
                    async with ClientSession(read_stream, write_stream) as session:
                        logger.info(f"Getting tools from SSE server {self.server_name}")
                        await session.initialize()
                        tools_result = await session.list_tools()
                        return [tool.dict() for tool in tools_result.tools]
                        
        except Exception as e:
            logger.error(f"Error getting tools: {e}")
            return []
    
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool using the official MCP client library"""
        if not self.mcp_available:
            logger.error("Cannot call tool: MCP client library not available")
            return {"error": "MCP client library not available"}
        
        try:
            from mcp.client.stdio import stdio_client
            from mcp.client.sse import sse_client
            from mcp import ClientSession
            
            if self.connection_type == "stdio":
                # Use stdio client
                server_params = self.config.get("server_params", {})
                async with stdio_client(server_params) as (read_stream, write_stream):
                    async with ClientSession(read_stream, write_stream) as session:
                        await session.initialize()
                        result = await session.call_tool(name=tool_name, arguments=params)
                        return {"content": result.content}
            else:
                # Use SSE client
                sse_url = self.config.get("url", "")
                headers = self.config.get("headers", {})
                
                if "Accept" not in headers:
                    headers["Accept"] = "text/event-stream"
                    
                async with sse_client(sse_url, headers=headers) as (read_stream, write_stream):
                    async with ClientSession(read_stream, write_stream) as session:
                        await session.initialize()
                        result = await session.call_tool(name=tool_name, arguments=params)
                        return {"content": result.content}
                        
        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}")
            return {"error": f"工具调用失败: {str(e)}"}
