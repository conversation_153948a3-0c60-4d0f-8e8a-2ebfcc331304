#!/bin/bash

echo "========================================"
echo "        启动MCP插件市场"
echo "========================================"

# 设置Python路径
PYTHON_PATH=python3

# 检查Python是否安装
$PYTHON_PATH --version > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Python未安装，请先安装Python 3.8或更高版本"
    exit 1
fi

# 检查是否需要安装依赖
if [ ! -d "venv" ]; then
    echo "首次运行，创建虚拟环境并安装依赖..."
    $PYTHON_PATH -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
else
    source venv/bin/activate
fi

# 设置环境变量
DATA_DIR="mcp_market_data"
HOST="0.0.0.0"
PORT="8000"
PLUGIN_PORT_BASE="9000"

# 启动MCP插件市场
echo "启动MCP插件市场..."
echo "访问地址: http://localhost:$PORT"
$PYTHON_PATH run.py --host $HOST --port $PORT --data-dir $DATA_DIR --plugin-port-base $PLUGIN_PORT_BASE

# 捕获Ctrl+C
trap 'echo "停止MCP插件市场..."; exit 0' INT

# 等待用户输入
read -p "按Enter键退出..." 