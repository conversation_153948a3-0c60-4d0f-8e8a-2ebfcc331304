"""
Services for AI Chat application
"""
import logging
from typing import List, Dict, Any, Optional
from .models import Message, Conversation, Model

logger = logging.getLogger(__name__)

class ChatService:
    """Service for chat operations"""
    
    def __init__(self):
        """Initialize the chat service"""
        self.conversations = {}
        self.models = {}
    
    def get_conversation(self, conversation_id: str) -> Conversation:
        """
        Get a conversation by ID
        
        Args:
            conversation_id: Conversation ID
            
        Returns:
            Conversation object
        """
        if conversation_id not in self.conversations:
            self.conversations[conversation_id] = Conversation(id=conversation_id)
        return self.conversations[conversation_id]
    
    def add_message(self, conversation_id: str, role: str, content: str) -> Message:
        """
        Add a message to a conversation
        
        Args:
            conversation_id: Conversation ID
            role: Message role (user or assistant)
            content: Message content
            
        Returns:
            Added message
        """
        conversation = self.get_conversation(conversation_id)
        message = Message(role=role, content=content)
        conversation.messages.append(message)
        return message
    
    def get_models(self) -> List[Model]:
        """
        Get all available models
        
        Returns:
            List of available models
        """
        return list(self.models.values())
    
    def add_model(self, model: Model) -> Model:
        """
        Add a new model
        
        Args:
            model: Model to add
            
        Returns:
            Added model
        """
        self.models[model.name] = model
        return model
    
    def get_model(self, name: str) -> Optional[Model]:
        """
        Get a model by name
        
        Args:
            name: Model name
            
        Returns:
            Model if found, None otherwise
        """
        return self.models.get(name) 