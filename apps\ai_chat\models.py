"""
Data models for AI Chat application
"""
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Optional, Any
from datetime import datetime

class Message(BaseModel):
    """Chat message model"""
    role: str
    content: str
    timestamp: datetime = Field(default_factory=datetime.now)

class Conversation(BaseModel):
    """Conversation model"""
    id: str
    messages: List[Message] = []
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = {}

class Model(BaseModel):
    """AI model configuration"""
    name: str
    provider: str
    api_key: Optional[str] = None
    api_url: Optional[str] = None
    model_name: Optional[str] = None
    
    class Config:
        """Pydantic model configuration"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        } 