"""
File utility functions
"""
import os
import logging
import json
import yaml
import shutil
from typing import Dict, Any, List, Optional, Union

logger = logging.getLogger(__name__)

def ensure_dir(directory: str) -> bool:
    """
    Ensure a directory exists, creating it if necessary
    
    Args:
        directory: Directory path
        
    Returns:
        True if directory exists or was created, False otherwise
    """
    try:
        if not os.path.exists(directory):
            os.makedirs(directory)
        return True
    except Exception as e:
        logger.error(f"Error creating directory {directory}: {e}")
        return False

def read_json(file_path: str, default: Any = None) -> Any:
    """
    Read JSON from a file
    
    Args:
        file_path: Path to JSON file
        default: Default value if file does not exist or is invalid
        
    Returns:
        Parsed JSON data or default value
    """
    if not os.path.exists(file_path):
        return default
    
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error reading JSON file {file_path}: {e}")
        return default

def write_json(file_path: str, data: Any, indent: int = 2) -> bool:
    """
    Write JSON to a file
    
    Args:
        file_path: Path to JSON file
        data: Data to write
        indent: JSON indentation
        
    Returns:
        True if successful, False otherwise
    """
    try:
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)
        
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=indent, ensure_ascii=False)
        return True
    except Exception as e:
        logger.error(f"Error writing JSON file {file_path}: {e}")
        return False

def read_yaml(file_path: str, default: Any = None) -> Any:
    """
    Read YAML from a file
    
    Args:
        file_path: Path to YAML file
        default: Default value if file does not exist or is invalid
        
    Returns:
        Parsed YAML data or default value
    """
    if not os.path.exists(file_path):
        return default
    
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"Error reading YAML file {file_path}: {e}")
        return default

def write_yaml(file_path: str, data: Any) -> bool:
    """
    Write YAML to a file
    
    Args:
        file_path: Path to YAML file
        data: Data to write
        
    Returns:
        True if successful, False otherwise
    """
    try:
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)
        
        with open(file_path, "w", encoding="utf-8") as f:
            yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
        return True
    except Exception as e:
        logger.error(f"Error writing YAML file {file_path}: {e}")
        return False

def copy_file(source: str, destination: str) -> bool:
    """
    Copy a file
    
    Args:
        source: Source file path
        destination: Destination file path
        
    Returns:
        True if successful, False otherwise
    """
    try:
        directory = os.path.dirname(destination)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)
        
        shutil.copy2(source, destination)
        return True
    except Exception as e:
        logger.error(f"Error copying file from {source} to {destination}: {e}")
        return False

def list_files(directory: str, pattern: str = None) -> List[str]:
    """
    List files in a directory
    
    Args:
        directory: Directory path
        pattern: File pattern to match
        
    Returns:
        List of file paths
    """
    if not os.path.exists(directory):
        return []
    
    try:
        if pattern:
            import fnmatch
            return [os.path.join(directory, f) for f in os.listdir(directory) 
                    if os.path.isfile(os.path.join(directory, f)) and fnmatch.fnmatch(f, pattern)]
        else:
            return [os.path.join(directory, f) for f in os.listdir(directory) 
                    if os.path.isfile(os.path.join(directory, f))]
    except Exception as e:
        logger.error(f"Error listing files in directory {directory}: {e}")
        return [] 