"""
Routes for MCP Market application
"""
import os
import logging
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates

# Import services
from .plugin_manager import PluginManager
from .process_manager import ProcessManager

logger = logging.getLogger(__name__)

# Setup templates
templates_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "templates")
templates = Jinja2Templates(directory=templates_dir)

def setup_routes(app: FastAPI):
    """
    Setup routes for the MCP Market application

    Args:
        app: FastAPI application
    """
    # Initialize services
    plugin_manager = PluginManager()
    process_manager = ProcessManager()

    # Main page route
    @app.get("/", response_class=HTMLResponse)
    async def index(request: Request):
        """
        Main page for MCP Market
        """
        return templates.TemplateResponse("index.html", {"request": request})