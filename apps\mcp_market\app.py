"""
MCP Market application initialization
"""
import os
import logging
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

# Import routes
from .routes import setup_routes

logger = logging.getLogger(__name__)

def create_mcp_market_app():
    """
    Create and configure the MCP Market application
    
    Returns:
        FastAPI application
    """
    # Create application
    app = FastAPI(title="MCP Market", description="MCP Plugin Marketplace")
    
    # Setup static files
    static_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "static")
    if os.path.exists(static_dir):
        app.mount("/static", StaticFiles(directory=static_dir), name="static")
    
    # Setup routes
    setup_routes(app)
    
    return app 