"""
API路由模块 - 定义MCP插件市场的API路由
"""
import os
import logging
from typing import Dict, List, Optional, Any, Union
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Body, Query, Path
from pydantic import BaseModel, Field

from mcp_market.models.plugin import Plugin, PluginStatus

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api", tags=["api"])
dependencies_router = APIRouter(prefix="/api", tags=["dependencies"])  # 修改前缀为/api而不是/api/dependencies

# 用于获取插件管理器的依赖函数
def get_plugin_manager():
    """获取插件管理器实例
    
    这个函数会在app.py中被覆盖，以提供真正的插件管理器实例
    """
    raise NotImplementedError("This function should be overridden in app.py")


# ============ 插件相关路由 ============

class PluginResponse(BaseModel):
    """插件响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


class AddPluginRequest(BaseModel):
    """添加插件请求"""
    repo_url: str = Field(..., description="仓库URL")
    branch: Optional[str] = Field("master", description="分支名称")
    type: Optional[str] = Field("git", description="插件类型")


@router.get("/plugins", response_model=PluginResponse)
async def list_plugins(plugin_manager=Depends(get_plugin_manager)):
    """获取所有插件"""
    try:
        plugins = plugin_manager.get_all_plugins()
        plugins_data = {pid: plugin.to_dict() for pid, plugin in plugins.items()}
        return {
            "success": True,
            "data": plugins_data
        }
    except Exception as e:
        logger.exception("获取插件列表时出错")
        return {
            "success": False,
            "message": f"获取插件列表失败: {str(e)}"
        }


@router.get("/plugins/running", response_model=PluginResponse)
async def list_running_plugins(plugin_manager=Depends(get_plugin_manager)):
    """获取正在运行的插件"""
    try:
        plugins = plugin_manager.get_all_plugins()
        running_plugins = {}
        
        for pid, plugin in plugins.items():
            if plugin.status == PluginStatus.RUNNING:
                running_plugins[pid] = plugin.to_dict()
        
        return {
            "success": True,
            "data": running_plugins
        }
    except Exception as e:
        logger.exception("获取运行中插件列表时出错")
        return {
            "success": False,
            "message": f"获取运行中插件列表失败: {str(e)}"
        }


@router.get("/plugins/available", response_model=PluginResponse)
async def list_available_plugins(plugin_manager=Depends(get_plugin_manager)):
    """获取可用的插件"""
    try:
        plugins = plugin_manager.get_all_plugins()
        available_plugins = {}
        
        for pid, plugin in plugins.items():
            if plugin.status == PluginStatus.INSTALLED or plugin.status == PluginStatus.RUNNING or plugin.status == PluginStatus.ERROR:
                available_plugins[pid] = plugin.to_dict()
        
        return {
            "success": True,
            "data": available_plugins
        }
    except Exception as e:
        logger.exception("获取可用插件列表时出错")
        return {
            "success": False,
            "message": f"获取可用插件列表失败: {str(e)}"
        }


@router.get("/plugins/installed", response_model=PluginResponse)
async def list_installed_plugins(plugin_manager=Depends(get_plugin_manager)):
    """获取已安装的插件"""
    try:
        plugins = plugin_manager.get_all_plugins()
        installed_plugins = {}
        
        for pid, plugin in plugins.items():
            # Include both INSTALLED and ERROR states in the installed plugins list
            if plugin.status == PluginStatus.INSTALLED or plugin.status == PluginStatus.ERROR:
                installed_plugins[pid] = plugin.to_dict()
        
        return {
            "success": True,
            "data": installed_plugins
        }
    except Exception as e:
        logger.exception("获取已安装插件列表时出错")
        return {
            "success": False,
            "message": f"获取已安装插件列表失败: {str(e)}"
        }


@router.post("/plugins/add", response_model=PluginResponse)
async def add_plugin(
    request: AddPluginRequest,
    plugin_manager=Depends(get_plugin_manager)
):
    """添加插件（新增的兼容前端的路由）"""
    try:
        source_url = request.repo_url
        # 如果branch不是master，将其添加到URL中
        if hasattr(request, 'branch') and request.branch and request.branch != "master":
            if "github.com" in source_url or "gitee.com" in source_url:
                # 检查URL是否已经包含分支信息
                if "/tree/" not in source_url:
                    source_url = f"{source_url}/tree/{request.branch}"
        
        # 确定插件类型
        source_type = request.type if hasattr(request, 'type') and request.type else "git"
        
        success, plugin_id, error = plugin_manager.install_plugin(
            source_url=source_url,
            source_type=source_type
        )
        
        if not success:
            return {
                "success": False,
                "message": f"添加插件失败: {error}"
            }
        
        return {
            "success": True,
            "message": f"插件添加成功: {plugin_id}",
            "data": {"plugin_id": plugin_id}
        }
    except Exception as e:
        logger.exception("添加插件时出错")
        return {
            "success": False,
            "message": f"添加插件失败: {str(e)}"
        }


@router.get("/plugins/{plugin_id}", response_model=PluginResponse)
async def get_plugin(plugin_id: str, plugin_manager=Depends(get_plugin_manager)):
    """获取插件详情"""
    try:
        plugin = plugin_manager.get_plugin(plugin_id)
        if not plugin:
            return {
                "success": False,
                "message": f"插件不存在: {plugin_id}"
            }
        
        return {
            "success": True,
            "data": plugin.to_dict()
        }
    except Exception as e:
        logger.exception(f"获取插件详情时出错: {plugin_id}")
        return {
            "success": False,
            "message": f"获取插件详情失败: {str(e)}"
        }


class InstallPluginRequest(BaseModel):
    """安装插件请求"""
    source_url: str = Field(..., description="源URL")
    source_type: str = Field("git", description="源类型")


@router.post("/plugins/install", response_model=PluginResponse)
async def install_plugin(
    request: InstallPluginRequest,
    background_tasks: BackgroundTasks,
    plugin_manager=Depends(get_plugin_manager)
):
    """安装插件"""
    try:
        success, plugin_id, error = plugin_manager.install_plugin(
            source_url=request.source_url,
            source_type=request.source_type
        )
        
        if not success:
            return {
                "success": False,
                "message": f"安装插件失败: {error}"
            }
        
        return {
            "success": True,
            "message": f"插件安装成功: {plugin_id}",
            "data": {"plugin_id": plugin_id}
        }
    except Exception as e:
        logger.exception("安装插件时出错")
        return {
            "success": False,
            "message": f"安装插件失败: {str(e)}"
        }


@router.post("/plugins/{plugin_id}/start", response_model=PluginResponse)
async def start_plugin(plugin_id: str, plugin_manager=Depends(get_plugin_manager)):
    """启动插件"""
    try:
        success, error, port = plugin_manager.start_plugin(plugin_id)
        if not success:
            return {
                "success": False,
                "message": f"启动插件失败: {error}"
            }
        
        return {
            "success": True,
            "message": f"插件启动成功",
            "data": {"port": port}
        }
    except Exception as e:
        logger.exception(f"启动插件时出错: {plugin_id}")
        return {
            "success": False,
            "message": f"启动插件失败: {str(e)}"
        }


@router.post("/plugins/{plugin_id}/stop", response_model=PluginResponse)
async def stop_plugin(plugin_id: str, plugin_manager=Depends(get_plugin_manager)):
    """停止插件"""
    try:
        success, error = plugin_manager.stop_plugin(plugin_id)
        if not success:
            return {
                "success": False,
                "message": f"停止插件失败: {error}"
            }
        
        return {
            "success": True,
            "message": "插件停止成功"
        }
    except Exception as e:
        logger.exception(f"停止插件时出错: {plugin_id}")
        return {
            "success": False,
            "message": f"停止插件失败: {str(e)}"
        }


@router.post("/plugins/{plugin_id}/restart", response_model=PluginResponse)
async def restart_plugin(plugin_id: str, plugin_manager=Depends(get_plugin_manager)):
    """重启插件"""
    try:
        success, error, port = plugin_manager.restart_plugin(plugin_id)
        if not success:
            return {
                "success": False,
                "message": f"重启插件失败: {error}"
            }
        
        return {
            "success": True,
            "message": "插件重启成功",
            "data": {"port": port}
        }
    except Exception as e:
        logger.exception(f"重启插件时出错: {plugin_id}")
        return {
            "success": False,
            "message": f"重启插件失败: {str(e)}"
        }


@router.post("/plugins/{plugin_id}/uninstall", response_model=PluginResponse)
async def uninstall_plugin(plugin_id: str, plugin_manager=Depends(get_plugin_manager)):
    """卸载插件"""
    try:
        success, error = plugin_manager.uninstall_plugin(plugin_id)
        if not success:
            return {
                "success": False,
                "message": f"卸载插件失败: {error}"
            }
        
        return {
            "success": True,
            "message": "插件卸载成功"
        }
    except Exception as e:
        logger.exception(f"卸载插件时出错: {plugin_id}")
        return {
            "success": False,
            "message": f"卸载插件失败: {str(e)}"
        }


@router.post("/plugins/{plugin_id}/update", response_model=PluginResponse)
async def update_plugin(plugin_id: str, plugin_manager=Depends(get_plugin_manager)):
    """更新插件"""
    try:
        success, error = plugin_manager.update_plugin(plugin_id)
        if not success:
            return {
                "success": False,
                "message": f"更新插件失败: {error}"
            }
        
        return {
            "success": True,
            "message": "插件更新成功"
        }
    except Exception as e:
        logger.exception(f"更新插件时出错: {plugin_id}")
        return {
            "success": False,
            "message": f"更新插件失败: {str(e)}"
        }


@router.post("/plugins/{plugin_id}/install", response_model=PluginResponse)
async def install_specific_plugin(plugin_id: str, plugin_manager=Depends(get_plugin_manager)):
    """安装特定ID的插件"""
    try:
        # 检查插件是否已经存在
        plugin = plugin_manager.get_plugin(plugin_id)
        if plugin:
            # 如果插件已经存在且状态不是ERROR，返回成功
            if plugin.status != PluginStatus.ERROR:
                return {
                    "success": True,
                    "message": f"插件已安装: {plugin_id}",
                    "data": {"plugin_id": plugin_id}
                }
            
            # 如果是ERROR状态，尝试更新或重新安装
            source_url = plugin.source_url
            source_type = plugin.source.value if hasattr(plugin.source, 'value') else str(plugin.source)
            
            # 先尝试删除插件
            plugin_manager.uninstall_plugin(plugin_id)
            
            # 重新安装
            success, new_plugin_id, error = plugin_manager.install_plugin(
                source_url=source_url,
                source_type=source_type
            )
            
            if not success:
                return {
                    "success": False,
                    "message": f"重新安装插件失败: {error}"
                }
            
            return {
                "success": True,
                "message": f"插件重新安装成功: {new_plugin_id}",
                "data": {"plugin_id": new_plugin_id}
            }
        else:
            # 插件不存在，返回错误
            return {
                "success": False,
                "message": f"插件不存在: {plugin_id}"
            }
    except Exception as e:
        logger.exception(f"安装特定插件时出错: {plugin_id}")
        return {
            "success": False,
            "message": f"安装特定插件失败: {str(e)}"
        }


# ============ 依赖相关路由 ============

class CreateDependencyRequest(BaseModel):
    """创建依赖组请求"""
    name: str = Field(..., description="依赖组名称")
    python_version: str = Field("3.8", description="Python版本")
    base_packages: List[str] = Field(default_factory=list, description="基础依赖包列表")

@dependencies_router.post("/dependencies", response_model=PluginResponse)
async def create_dependency_group(request: CreateDependencyRequest, plugin_manager=Depends(get_plugin_manager)):
    """创建新的依赖组"""
    try:
        group, error = plugin_manager.dep_manager.create_dependency_group(
            name=request.name,
            base_packages=request.base_packages,
            python_version=request.python_version
        )
        
        if not group:
            return {
                "success": False,
                "message": f"创建依赖组失败: {error}"
            }
        
        # 保存依赖组到插件管理器
        plugin_manager.dependency_groups[group.id] = group
        plugin_manager.storage.save_dependency_group(group)
        
        return {
            "success": True,
            "message": "依赖组创建成功",
            "data": group.to_dict()
        }
    except Exception as e:
        logger.exception("创建依赖组时出错")
        return {
            "success": False,
            "message": f"创建依赖组失败: {str(e)}"
        }

@dependencies_router.get("/dependencies", response_model=PluginResponse)
async def list_dependencies(plugin_manager=Depends(get_plugin_manager)):
    """获取所有依赖组"""
    try:
        groups = plugin_manager.dependency_groups
        groups_data = {gid: group.to_dict() for gid, group in groups.items()}
        return {
            "success": True,
            "data": groups_data
        }
    except Exception as e:
        logger.exception("获取依赖组列表时出错")
        return {
            "success": False,
            "message": f"获取依赖组列表失败: {str(e)}"
        }

@dependencies_router.get("/dependencies/groups", response_model=PluginResponse)
async def list_dependency_groups(plugin_manager=Depends(get_plugin_manager)):
    """获取所有依赖组"""
    try:
        groups = plugin_manager.dependency_groups
        groups_data = {gid: group.to_dict() for gid, group in groups.items()}
        return {
            "success": True,
            "data": groups_data
        }
    except Exception as e:
        logger.exception("获取依赖组列表时出错")
        return {
            "success": False,
            "message": f"获取依赖组列表失败: {str(e)}"
        }

@dependencies_router.get("/dependencies/{group_id}", response_model=PluginResponse)
async def get_dependency_group(group_id: str, plugin_manager=Depends(get_plugin_manager)):
    """获取特定依赖组的详细信息"""
    try:
        group = plugin_manager.dependency_groups.get(group_id)
        if not group:
            return {
                "success": False,
                "message": f"依赖组不存在: {group_id}"
            }
        
        # 获取使用此依赖组的插件列表
        plugins = []
        for plugin_id, plugin in plugin_manager.plugins.items():
            if plugin.dependency_group == group_id:
                plugins.append({
                    "id": plugin.id,
                    "name": plugin.name,
                    "status": plugin.status.value
                })
        
        # 获取更详细的组信息
        group_info = group.to_dict()
        group_info["plugins"] = plugins
        
        return {
            "success": True,
            "data": group_info
        }
    except Exception as e:
        logger.exception(f"获取依赖组详情时出错: {group_id}")
        return {
            "success": False,
            "message": f"获取依赖组详情失败: {str(e)}"
        }

@dependencies_router.delete("/dependencies/{group_id}", response_model=PluginResponse)
async def delete_dependency_group(group_id: str, plugin_manager=Depends(get_plugin_manager)):
    """删除依赖组"""
    try:
        group = plugin_manager.dependency_groups.get(group_id)
        if not group:
            return {
                "success": False,
                "message": f"依赖组不存在: {group_id}"
            }
        
        # 检查是否有插件正在使用此依赖组
        for plugin_id, plugin in plugin_manager.plugins.items():
            if plugin.dependency_group == group_id:
                return {
                    "success": False,
                    "message": f"无法删除依赖组: 正在被插件 {plugin.name or plugin_id} 使用"
                }
        
        # 删除依赖组
        success = plugin_manager.dep_manager.delete_dependency_group(group)
        if not success:
            return {
                "success": False,
                "message": "删除依赖组环境失败"
            }
        
        # 从存储中删除
        plugin_manager.storage.delete_dependency_group(group_id)
        
        # 从内存中删除
        if group_id in plugin_manager.dependency_groups:
            del plugin_manager.dependency_groups[group_id]
        
        return {
            "success": True,
            "message": "依赖组删除成功"
        }
    except Exception as e:
        logger.exception(f"删除依赖组时出错: {group_id}")
        return {
            "success": False,
            "message": f"删除依赖组失败: {str(e)}"
        } 