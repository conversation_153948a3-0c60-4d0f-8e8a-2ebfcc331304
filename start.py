"""
启动统一应用 - AI助手与MCP插件市场
"""
import os
import sys
import subprocess


def start_unified_app():
    """启动统一应用"""
    print("启动统一应用...")
    
    # 使用新的main.py作为入口
    app_file = "main.py"
    
    # 判断是否存在
    if not os.path.exists(app_file):
        print(f"错误: 未找到主程序文件 {app_file}！")
        return False
    
    # 启动应用
    try:
        subprocess.run([sys.executable, app_file], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"启动应用失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n应用已停止")
        return True

if __name__ == "__main__":
    print("=" * 50)
    print("AI助手与MCP插件市场 - 启动程序")
    print("=" * 50)
    # 输出访问地址
    print("📱 应用将在以下地址启动:")
    print("   主页:       http://localhost:8081/")
    print("   AI对话:     http://localhost:8081/ai-chat/")
    print("   插件市场:   http://localhost:8081/mcp-market/")
    print("=" * 50)
    
    # 启动统一应用
    success = start_unified_app()
    if not success:
        sys.exit(1) 
