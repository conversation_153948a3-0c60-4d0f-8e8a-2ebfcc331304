"""
HTTP utility functions
"""
import logging
import requests
import json
from typing import Dict, Any, Optional, Union
import asyncio
import httpx

logger = logging.getLogger(__name__)

async def async_get(url: str, headers: Dict[str, str] = None, 
                   params: Dict[str, Any] = None, timeout: int = 30) -> Dict[str, Any]:
    """
    Make an asynchronous GET request
    
    Args:
        url: URL to request
        headers: Request headers
        params: Request parameters
        timeout: Request timeout in seconds
        
    Returns:
        Response data as dictionary
    """
    headers = headers or {}
    params = params or {}
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, params=params, timeout=timeout)
            response.raise_for_status()
            return response.json()
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error: {e}")
        return {"error": str(e), "status_code": e.response.status_code}
    except Exception as e:
        logger.error(f"Request error: {e}")
        return {"error": str(e)}

async def async_post(url: str, headers: Dict[str, str] = None, 
                    data: Dict[str, Any] = None, json_data: Dict[str, Any] = None,
                    timeout: int = 30) -> Dict[str, Any]:
    """
    Make an asynchronous POST request
    
    Args:
        url: URL to request
        headers: Request headers
        data: Form data
        json_data: JSON data
        timeout: Request timeout in seconds
        
    Returns:
        Response data as dictionary
    """
    headers = headers or {}
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url, 
                headers=headers, 
                data=data, 
                json=json_data, 
                timeout=timeout
            )
            response.raise_for_status()
            return response.json()
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error: {e}")
        return {"error": str(e), "status_code": e.response.status_code}
    except Exception as e:
        logger.error(f"Request error: {e}")
        return {"error": str(e)}

def get(url: str, headers: Dict[str, str] = None, 
       params: Dict[str, Any] = None, timeout: int = 30) -> Dict[str, Any]:
    """
    Make a synchronous GET request
    
    Args:
        url: URL to request
        headers: Request headers
        params: Request parameters
        timeout: Request timeout in seconds
        
    Returns:
        Response data as dictionary
    """
    headers = headers or {}
    params = params or {}
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=timeout)
        response.raise_for_status()
        return response.json()
    except requests.HTTPError as e:
        logger.error(f"HTTP error: {e}")
        return {"error": str(e), "status_code": e.response.status_code}
    except Exception as e:
        logger.error(f"Request error: {e}")
        return {"error": str(e)}

def post(url: str, headers: Dict[str, str] = None, 
        data: Dict[str, Any] = None, json_data: Dict[str, Any] = None,
        timeout: int = 30) -> Dict[str, Any]:
    """
    Make a synchronous POST request
    
    Args:
        url: URL to request
        headers: Request headers
        data: Form data
        json_data: JSON data
        timeout: Request timeout in seconds
        
    Returns:
        Response data as dictionary
    """
    headers = headers or {}
    
    try:
        response = requests.post(
            url, 
            headers=headers, 
            data=data, 
            json=json_data, 
            timeout=timeout
        )
        response.raise_for_status()
        return response.json()
    except requests.HTTPError as e:
        logger.error(f"HTTP error: {e}")
        return {"error": str(e), "status_code": e.response.status_code}
    except Exception as e:
        logger.error(f"Request error: {e}")
        return {"error": str(e)} 