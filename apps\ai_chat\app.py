"""
AI Chat application initialization
"""
import os
import logging
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

# Import routes
from .routes import setup_routes

logger = logging.getLogger(__name__)

def create_ai_chat_app():
    """
    Create and configure the AI Chat application
    
    Returns:
        FastAPI application
    """
    # Create application
    app = FastAPI(title="AI Chat", description="AI Chat Application")
    
    # Setup static files
    static_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "static")
    if os.path.exists(static_dir):
        app.mount("/static", StaticFiles(directory=static_dir), name="static")
    
    # Setup routes
    setup_routes(app)
    
    return app 