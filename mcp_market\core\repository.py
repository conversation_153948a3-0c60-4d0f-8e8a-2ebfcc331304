"""
仓库管理模块 - 处理远程代码仓库的克隆、更新和管理
"""
import os
import shutil
import subprocess
import logging
import json
import re
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import uuid
import tempfile

from mcp_market.models.plugin import Plugin, PluginSource, PluginManifest, PluginStatus

logger = logging.getLogger(__name__)


class RepositoryManager:
    """管理远程代码仓库的克隆、更新和删除"""
    
    def __init__(self, plugins_dir: str, temp_dir: Optional[str] = None):
        """初始化仓库管理器
        
        Args:
            plugins_dir: 插件目录路径
            temp_dir: 临时目录路径，默认使用系统临时目录
        """
        self.plugins_dir = Path(plugins_dir)
        self.temp_dir = Path(temp_dir) if temp_dir else Path(tempfile.gettempdir()) / "mcp_market"
        
        # 确保目录存在
        os.makedirs(self.plugins_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        logger.info(f"初始化仓库管理器: 插件目录={self.plugins_dir}, 临时目录={self.temp_dir}")
    
    def clone_repository(self, repo_url: str, branch: Optional[str] = None, 
                        git_credentials: Optional[Dict[str, str]] = None, 
                        depth: int = 1) -> Tuple[bool, str, Optional[str]]:
        """克隆远程仓库
        
        Args:
            repo_url: 仓库URL
            branch: 分支名称，默认为默认分支
            git_credentials: Git凭证，包含username/password或token
            depth: 克隆深度，默认为1（浅克隆）
        
        Returns:
            (成功标志, 本地路径, 错误信息)
        """
        # 生成唯一目录名
        repo_id = str(uuid.uuid4())
        local_path = self.plugins_dir / repo_id
        
        # 构建克隆命令
        cmd = ["git", "clone"]
        
        if depth > 0:
            cmd.extend(["--depth", str(depth)])
        
        if branch:
            cmd.extend(["--branch", branch])
        
        # 处理凭证
        modified_url = repo_url
        env = os.environ.copy()
        
        if git_credentials:
            if 'token' in git_credentials:
                # 对于HTTP/HTTPS URL，添加token到URL
                if repo_url.startswith("http"):
                    url_parts = repo_url.split("://")
                    if len(url_parts) == 2:
                        protocol, path = url_parts
                        modified_url = f"{protocol}://oauth2:{git_credentials['token']}@{path}"
                else:
                    # 对于SSH等其他URL，设置环境变量
                    env["GIT_ASKPASS"] = "echo"
                    env["GIT_ACCESS_TOKEN"] = git_credentials['token']
            elif 'username' in git_credentials and 'password' in git_credentials:
                # 对于HTTP/HTTPS URL，添加用户名和密码
                if repo_url.startswith("http"):
                    url_parts = repo_url.split("://")
                    if len(url_parts) == 2:
                        protocol, path = url_parts
                        modified_url = f"{protocol}://{git_credentials['username']}:{git_credentials['password']}@{path}"
                else:
                    # 对于SSH等其他URL，设置环境变量
                    env["GIT_ASKPASS"] = "echo"
                    env["GIT_USERNAME"] = git_credentials['username']
                    env["GIT_PASSWORD"] = git_credentials['password']
        
        cmd.extend([modified_url, str(local_path)])
        
        logger.info(f"克隆仓库: {repo_url} -> {local_path}")
        logger.debug(f"执行命令: git clone [凭证已隐藏] {str(local_path)}")
        
        try:
            # 执行克隆命令
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False,
                env=env
            )
            
            if result.returncode != 0:
                error_msg = result.stderr.strip()
                logger.error(f"克隆仓库失败: {error_msg}")
                return False, "", error_msg
            
            logger.info(f"仓库克隆成功: {local_path}")
            return True, str(local_path), None
        
        except Exception as e:
            logger.exception(f"克隆仓库时发生异常: {e}")
            
            # 清理可能部分创建的目录
            if local_path.exists():
                shutil.rmtree(local_path, ignore_errors=True)
            
            return False, "", str(e)
    
    def update_repository(self, local_path: str, branch: Optional[str] = None, 
                         git_credentials: Optional[Dict[str, str]] = None) -> Tuple[bool, Optional[str]]:
        """更新本地仓库
        
        Args:
            local_path: 本地仓库路径
            branch: 要切换的分支名称，默认不切换
            git_credentials: Git凭证，包含username/password或token
        
        Returns:
            (成功标志, 错误信息)
        """
        if not os.path.exists(local_path):
            return False, f"本地仓库不存在: {local_path}"
        
        logger.info(f"更新仓库: {local_path}")
        
        try:
            # 准备环境变量（处理凭证）
            env = os.environ.copy()
            
            if git_credentials:
                if 'token' in git_credentials:
                    env["GIT_ASKPASS"] = "echo"
                    env["GIT_ACCESS_TOKEN"] = git_credentials['token']
                elif 'username' in git_credentials and 'password' in git_credentials:
                    env["GIT_ASKPASS"] = "echo"
                    env["GIT_USERNAME"] = git_credentials['username']
                    env["GIT_PASSWORD"] = git_credentials['password']
            
            # 如果指定了分支，先切换
            if branch:
                checkout_cmd = ["git", "-C", local_path, "checkout", branch]
                checkout_result = subprocess.run(
                    checkout_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False,
                    env=env
                )
                
                if checkout_result.returncode != 0:
                    error_msg = checkout_result.stderr.strip()
                    logger.error(f"切换分支失败: {error_msg}")
                    return False, error_msg
            
            # 执行拉取操作
            pull_cmd = ["git", "-C", local_path, "pull"]
            pull_result = subprocess.run(
                pull_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False,
                env=env
            )
            
            if pull_result.returncode != 0:
                error_msg = pull_result.stderr.strip()
                logger.error(f"拉取更新失败: {error_msg}")
                return False, error_msg
            
            logger.info(f"仓库更新成功: {local_path}")
            return True, None
        
        except Exception as e:
            logger.exception(f"更新仓库时发生异常: {e}")
            return False, str(e)
    
    def delete_repository(self, local_path: str) -> bool:
        """删除本地仓库
        
        Args:
            local_path: 本地仓库路径
        
        Returns:
            是否成功
        """
        if not os.path.exists(local_path):
            return True  # 已经不存在，视为成功
        
        logger.info(f"删除仓库: {local_path}")
        
        try:
            shutil.rmtree(local_path, ignore_errors=True)
            return True
        except Exception as e:
            logger.exception(f"删除仓库时发生异常: {e}")
            return False
    
    def get_manifest(self, local_path: str) -> Tuple[Optional[PluginManifest], Optional[str]]:
        """获取插件清单
        
        Args:
            local_path: 本地仓库路径
        
        Returns:
            (清单对象, 错误信息)
        """
        if not os.path.exists(local_path):
            return None, f"本地仓库不存在: {local_path}"
        
        logger.info(f"获取插件清单: {local_path}")
        
        try:
            # 尝试从清单文件获取
            manifest_path = Path(local_path) / "mcp_manifest.json"
            if manifest_path.exists():
                with open(manifest_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    return self._parse_json_manifest(data, "mcp_manifest.json"), None
            
            # 尝试从package.json获取
            package_path = Path(local_path) / "package.json"
            if package_path.exists():
                with open(package_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    manifest = self._parse_json_manifest(data, "package.json")
                    if manifest:
                        return manifest, None
            
            # 尝试从pyproject.toml获取
            pyproject_path = Path(local_path) / "pyproject.toml"
            if pyproject_path.exists():
                manifest = self._parse_pyproject_toml(pyproject_path)
                if manifest:
                    return manifest, None
            
            # 如果没有找到标准清单文件，尝试推断项目信息
            return self._infer_project_info(local_path), None
        
        except Exception as e:
            logger.exception(f"获取插件清单时发生异常: {e}")
            return None, str(e)
    
    def _parse_json_manifest(self, data: Dict, file_name: str) -> Optional[PluginManifest]:
        """解析JSON格式的清单文件
        
        Args:
            data: JSON数据
            file_name: 文件名，用于日志记录
        
        Returns:
            清单对象
        """
        logger.debug(f"解析JSON清单文件: {file_name}")
        
        try:
            if file_name == "mcp_manifest.json":
                # 直接从专用清单文件创建对象
                return PluginManifest.from_dict(data)
            
            elif file_name == "package.json":
                # 从package.json提取信息
                if "name" not in data:
                    return None
                
                manifest_data = {
                    "name": data.get("name", ""),
                    "version": data.get("version", "0.1.0"),
                    "description": data.get("description", ""),
                    "author": data.get("author", ""),
                    "license": data.get("license", ""),
                    "dependencies": []
                }
                
                # 处理依赖
                dependencies = []
                
                # 检查package.json是否有mcp相关配置
                if "mcp" in data:
                    mcp_data = data["mcp"]
                    
                    # 更新清单数据
                    manifest_data.update({
                        "entry_point": mcp_data.get("entry_point", "main.py"),
                        "requires_python": mcp_data.get("requires_python", ">=3.8"),
                        "dependencies": mcp_data.get("dependencies", []),
                        "mcp_protocol_version": mcp_data.get("protocol_version", "1.0"),
                        "commands": mcp_data.get("commands", {}),
                        "environment_variables": mcp_data.get("environment_variables", {}),
                        "resource_limits": mcp_data.get("resource_limits", {}),
                        "metadata": mcp_data.get("metadata", {})
                    })
                
                return PluginManifest.from_dict(manifest_data)
            
            return None
        
        except Exception as e:
            logger.exception(f"解析JSON清单文件失败: {e}")
            return None
    
    def _parse_pyproject_toml(self, path: Path) -> Optional[PluginManifest]:
        """解析pyproject.toml文件
        
        Args:
            path: 文件路径
        
        Returns:
            清单对象
        """
        logger.debug(f"解析pyproject.toml文件: {path}")
        
        try:
            # pyproject.toml需要使用特定的解析库
            use_tomli = False
            try:
                import tomli as toml_parser
                use_tomli = True
            except ImportError:
                try:
                    import toml as toml_parser
                except ImportError:
                    logger.warning("缺少toml解析库，无法解析pyproject.toml文件")
                    return None
            
            # tomli requires binary mode, toml requires text mode
            if use_tomli:
                with open(path, "rb") as f:
                    data = toml_parser.load(f)
            else:
                with open(path, "r", encoding="utf-8") as f:
                    data = toml_parser.load(f)
            
            # 获取项目元数据
            project_data = data.get("tool", {}).get("poetry", {}) or data.get("project", {})
            
            if not project_data:
                logger.warning("pyproject.toml中没有找到项目元数据")
                return None
            
            manifest_data = {
                "name": project_data.get("name", ""),
                "version": project_data.get("version", "0.1.0"),
                "description": project_data.get("description", ""),
                "author": project_data.get("authors", [""])[0] if isinstance(project_data.get("authors", []), list) else project_data.get("authors", ""),
                "license": project_data.get("license", ""),
                "dependencies": []
            }
            
            # 处理依赖
            dependencies = []
            deps = project_data.get("dependencies", {})
            if isinstance(deps, dict):
                for dep_name, dep_version in deps.items():
                    if isinstance(dep_version, str):
                        dependencies.append(f"{dep_name}{dep_version}")
                    else:
                        dependencies.append(dep_name)
            
            manifest_data["dependencies"] = dependencies
            
            # 检查是否有MCP相关配置
            mcp_data = data.get("tool", {}).get("mcp", {})
            if mcp_data:
                # 更新清单数据
                manifest_data.update({
                    "entry_point": mcp_data.get("entry_point", "main.py"),
                    "requires_python": mcp_data.get("requires_python", ">=3.8"),
                    "mcp_protocol_version": mcp_data.get("protocol_version", "1.0"),
                    "commands": mcp_data.get("commands", {}),
                    "environment_variables": mcp_data.get("environment_variables", {}),
                    "resource_limits": mcp_data.get("resource_limits", {}),
                    "metadata": mcp_data.get("metadata", {})
                })
            
            return PluginManifest.from_dict(manifest_data)
            
        except Exception as e:
            logger.exception(f"解析pyproject.toml文件失败: {e}")
            return None
    
    def _infer_project_info(self, local_path: str) -> PluginManifest:
        """推断项目信息，当找不到标准清单文件时使用
        
        Args:
            local_path: 本地仓库路径
        
        Returns:
            推断的清单对象
        """
        logger.info(f"推断项目信息: {local_path}")
        
        # 获取目录名作为项目名
        name = os.path.basename(local_path)
        
        # 获取版本
        version = "0.1.0"
        
        # 查找README文件作为描述
        description = ""
        readme_paths = [
            os.path.join(local_path, "README.md"),
            os.path.join(local_path, "README"),
            os.path.join(local_path, "README.txt")
        ]
        
        for readme_path in readme_paths:
            if os.path.exists(readme_path):
                try:
                    with open(readme_path, "r", encoding="utf-8") as f:
                        description = f.read(500)  # 只读取前500个字符
                        # 提取第一行作为简短描述
                        first_line = description.split("\n", 1)[0].strip()
                        if first_line:
                            description = first_line
                        break
                except Exception:
                    pass
        
        # 查找主Python文件
        entry_point = "main.py"
        for candidate in ["main.py", "app.py", "__main__.py"]:
            if os.path.exists(os.path.join(local_path, candidate)):
                entry_point = candidate
                break
        
        # 查找requirements.txt文件获取依赖
        dependencies = []
        req_path = os.path.join(local_path, "requirements.txt")
        if os.path.exists(req_path):
            try:
                with open(req_path, "r", encoding="utf-8") as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith("#"):
                            dependencies.append(line)
            except Exception:
                pass
        
        # 创建清单对象
        return PluginManifest(
            name=name,
            version=version,
            description=description,
            entry_point=entry_point,
            author="",
            license="",
            requires_python=">=3.8",
            dependencies=dependencies,
            optional_dependencies={},
            mcp_protocol_version="1.0",
            commands={},
            environment_variables={},
            resource_limits={},
            metadata={"inferred": True}
        )
    
    def get_repository(self, source_url: str, source_type: str = "git") -> Tuple[bool, str, Optional[str]]:
        """获取仓库，根据类型执行克隆或复制操作
        
        Args:
            source_url: 源URL，可以是Git仓库、本地目录或压缩包路径
            source_type: 源类型，git、local或archive
        
        Returns:
            (成功标志, 本地路径, 错误信息)
        """
        logger.info(f"获取仓库: {source_url}, 类型: {source_type}")
        
        if source_type == "git" or source_type == PluginSource.GIT.value:
            # 解析URL，提取分支信息
            branch = None
            clean_url = source_url
            
            # 处理github/gitee的URL格式，可能包含分支信息
            if "/tree/" in source_url:
                parts = source_url.split("/tree/")
                if len(parts) == 2:
                    clean_url = parts[0]
                    branch = parts[1]
            
            # 执行Git克隆
            return self.clone_repository(clean_url, branch)
            
        elif source_type == "local" or source_type == PluginSource.LOCAL.value:
            # 本地目录，复制到插件目录
            try:
                # 生成唯一目录名
                repo_id = str(uuid.uuid4())
                local_path = self.plugins_dir / repo_id
                
                # 复制目录
                shutil.copytree(source_url, local_path)
                
                logger.info(f"本地目录复制成功: {source_url} -> {local_path}")
                return True, str(local_path), None
            except Exception as e:
                logger.exception(f"复制本地目录时发生异常: {e}")
                return False, "", str(e)
                
        elif source_type == "archive" or source_type == PluginSource.ARCHIVE.value:
            # 压缩包，解压到插件目录
            try:
                import zipfile
                import tarfile
                
                # 生成唯一目录名
                repo_id = str(uuid.uuid4())
                local_path = self.plugins_dir / repo_id
                os.makedirs(local_path, exist_ok=True)
                
                # 根据文件扩展名决定解压方法
                if source_url.endswith('.zip'):
                    with zipfile.ZipFile(source_url, 'r') as zip_ref:
                        zip_ref.extractall(local_path)
                elif source_url.endswith('.tar.gz') or source_url.endswith('.tgz'):
                    with tarfile.open(source_url, 'r:gz') as tar_ref:
                        tar_ref.extractall(local_path)
                elif source_url.endswith('.tar'):
                    with tarfile.open(source_url, 'r') as tar_ref:
                        tar_ref.extractall(local_path)
                else:
                    return False, "", f"不支持的压缩包格式: {source_url}"
                
                logger.info(f"压缩包解压成功: {source_url} -> {local_path}")
                return True, str(local_path), None
            except Exception as e:
                logger.exception(f"解压压缩包时发生异常: {e}")
                return False, "", str(e)
        else:
            return False, "", f"不支持的仓库类型: {source_type}"


def parse_repo_url(url: str) -> Tuple[PluginSource, str, str]:
    """解析仓库URL，识别类型并规范化
    
    Args:
        url: 仓库URL
    
    Returns:
        (仓库类型, 规范化URL, 仓库ID)
    """
    # 清理URL
    url = url.strip()
    
    # 识别URL类型
    source_type = PluginSource.GITHUB
    
    if "github.com" in url:
        source_type = PluginSource.GITHUB
    elif "gitlab.com" in url:
        source_type = PluginSource.GITLAB
    elif "bitbucket.org" in url:
        source_type = PluginSource.BITBUCKET
    else:
        source_type = PluginSource.OTHER
    
    # 规范化URL
    # 移除尾部.git
    if url.endswith(".git"):
        url = url[:-4]
    
    # 提取仓库ID - 通常是用户名/仓库名
    repo_id = ""
    
    # 提取用户名/仓库名
    if source_type in [PluginSource.GITHUB, PluginSource.GITLAB, PluginSource.BITBUCKET]:
        # 尝试匹配格式：[hostname]/username/repo
        match = re.search(r'[:/]([^/]+/[^/]+)$', url)
        if match:
            repo_id = match.group(1)
    
    if not repo_id:
        # 使用URL的哈希作为ID
        import hashlib
        repo_id = hashlib.md5(url.encode()).hexdigest()[:12]
    
    return source_type, url, repo_id 