"""
Main program entry point for AI Assistant and MCP Marketplace
"""
import os
import sys
import logging
import argparse

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="AI Assistant and MCP Marketplace")
    parser.add_argument("--host", help="Host to bind to", default=None)
    parser.add_argument("--port", type=int, help="Port to bind to", default=None)
    parser.add_argument("--env", choices=["dev", "prod"], help="Environment to run in", default="dev")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    return parser.parse_args()

def main():
    """Main entry point"""
    # Parse command line arguments
    args = parse_arguments()
    
    # Load appropriate configuration
    if args.env == "dev":
        from config.development import DEV_CONFIG as config
        logger.info("Using development configuration")
    else:
        from config.production import PROD_CONFIG as config
        logger.info("Using production configuration")
    
    # Override config with command line arguments
    if args.host:
        config["app"]["host"] = args.host
    if args.port:
        config["app"]["port"] = args.port
    if args.debug:
        config["app"]["debug"] = True
        config["app"]["reload"] = True
    
    # Print access information
    print("=" * 50)
    print("AI Assistant and MCP Marketplace")
    print("=" * 50)
    print(f"📱 Application will start at:")
    print(f"   Main page:     http://{config['app']['host']}:{config['app']['port']}/")
    print(f"   AI Chat:       http://{config['app']['host']}:{config['app']['port']}/ai-chat/")
    print(f"   MCP Market:    http://{config['app']['host']}:{config['app']['port']}/mcp-market/")
    print("=" * 50)
    
    # Run the application
    import uvicorn
    uvicorn.run(
        "app:app",
        host=config["app"]["host"],
        port=config["app"]["port"],
        reload=config["app"]["reload"]
    )

if __name__ == "__main__":
    main() 