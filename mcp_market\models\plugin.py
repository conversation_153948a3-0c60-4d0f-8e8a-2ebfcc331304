"""
插件模型 - 定义与插件相关的数据模型
"""
from enum import Enum
from typing import Dict, List, Optional, Any, Union
import uuid
from dataclasses import dataclass, field


class PluginStatus(Enum):
    """插件状态"""
    AVAILABLE = "available"     # 可用，但未安装
    INSTALLING = "installing"   # 正在安装
    INSTALLED = "installed"     # 已安装
    RUNNING = "running"         # 正在运行
    ERROR = "error"             # 错误状态
    UPDATING = "updating"       # 正在更新


class PluginSource(Enum):
    """插件源类型"""
    GIT = "git"                 # Git仓库
    LOCAL = "local"             # 本地目录
    ARCHIVE = "archive"         # 压缩包


@dataclass
class PluginManifest:
    """插件清单，描述插件的元数据"""
    name: str                                   # 插件名称
    version: str                                # 插件版本
    description: str = ""                       # 插件描述
    author: str = ""                            # 作者
    license: str = ""                           # 许可证
    entry_point: str = "main.py"                # 入口点
    requires_python: str = ">=3.8"              # Python版本要求
    dependencies: List[str] = field(default_factory=list)  # 依赖项
    optional_dependencies: Dict[str, List[str]] = field(default_factory=dict)  # 可选依赖项
    mcp_protocol_version: str = "1.0"           # MCP协议版本
    commands: Dict[str, Any] = field(default_factory=dict)  # 插件命令
    environment_variables: Dict[str, str] = field(default_factory=dict)  # 环境变量
    resource_limits: Dict[str, Any] = field(default_factory=dict)  # 资源限制
    metadata: Dict[str, Any] = field(default_factory=dict)  # 其他元数据
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PluginManifest':
        """从字典创建对象"""
        # 处理可能不存在的字段
        dependencies = data.get("dependencies", [])
        if isinstance(dependencies, Dict):
            # 处理依赖字典
            dependencies = [f"{name}{spec}" for name, spec in dependencies.items()]
        
        return cls(
            name=data.get("name", ""),
            version=data.get("version", "0.1.0"),
            description=data.get("description", ""),
            author=data.get("author", ""),
            license=data.get("license", ""),
            entry_point=data.get("entry_point", "main.py"),
            requires_python=data.get("requires_python", ">=3.8"),
            dependencies=dependencies,
            optional_dependencies=data.get("optional_dependencies", {}),
            mcp_protocol_version=data.get("mcp_protocol_version", "1.0"),
            commands=data.get("commands", {}),
            environment_variables=data.get("environment_variables", {}),
            resource_limits=data.get("resource_limits", {}),
            metadata=data.get("metadata", {})
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "author": self.author,
            "license": self.license,
            "entry_point": self.entry_point,
            "requires_python": self.requires_python,
            "dependencies": self.dependencies,
            "optional_dependencies": self.optional_dependencies,
            "mcp_protocol_version": self.mcp_protocol_version,
            "commands": self.commands,
            "environment_variables": self.environment_variables,
            "resource_limits": self.resource_limits,
            "metadata": self.metadata
        }


@dataclass
class Plugin:
    """插件对象，表示一个插件实例"""
    id: str                                     # 插件ID
    name: str                                   # 插件名称
    version: str                                # 版本
    description: str = ""                       # 描述
    status: PluginStatus = PluginStatus.AVAILABLE  # 状态
    source: PluginSource = PluginSource.GIT     # 源类型
    source_url: str = ""                        # 源URL
    local_path: str = ""                        # 本地路径
    dependency_group: Optional[str] = None      # 依赖组ID
    entry_point: str = "main.py"                # 入口点
    api_url: Optional[str] = None               # API URL
    error_message: Optional[str] = None         # 错误信息
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Plugin':
        """从字典创建对象"""
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            name=data.get("name", ""),
            version=data.get("version", ""),
            description=data.get("description", ""),
            status=PluginStatus(data.get("status", "available")),
            source=PluginSource(data.get("source", "git")),
            source_url=data.get("source_url", ""),
            local_path=data.get("local_path", ""),
            dependency_group=data.get("dependency_group"),
            entry_point=data.get("entry_point", "main.py"),
            api_url=data.get("api_url"),
            error_message=data.get("error_message"),
            metadata=data.get("metadata", {})
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "status": self.status.value,
            "source": self.source.value,
            "source_url": self.source_url,
            "local_path": self.local_path,
            "dependency_group": self.dependency_group,
            "entry_point": self.entry_point,
            "api_url": self.api_url,
            "error_message": self.error_message,
            "metadata": self.metadata
        }


@dataclass
class DependencyGroup:
    """依赖组，表示一组共享的Python依赖"""
    id: str                                     # 依赖组ID
    name: str                                   # 名称
    python_version: str                         # Python版本
    base_packages: List[str] = field(default_factory=list)  # 基础包
    specific_packages: Dict[str, List[str]] = field(default_factory=dict)  # 特定插件的包
    env_path: Optional[str] = None              # 环境路径
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DependencyGroup':
        """从字典创建对象"""
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            name=data.get("name", ""),
            python_version=data.get("python_version", "3.8"),
            base_packages=data.get("base_packages", []),
            specific_packages=data.get("specific_packages", {}),
            env_path=data.get("env_path")
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "python_version": self.python_version,
            "base_packages": self.base_packages,
            "specific_packages": self.specific_packages,
            "env_path": self.env_path
        } 