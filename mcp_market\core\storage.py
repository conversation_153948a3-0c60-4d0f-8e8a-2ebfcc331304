"""
存储模块 - 管理插件和依赖组的持久化存储
"""
import json
import os
import sqlite3
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import logging
from abc import ABC, abstractmethod

from mcp_market.models.plugin import Plugin, DependencyGroup

logger = logging.getLogger(__name__)


class Storage(ABC):
    """存储接口，定义插件和依赖组的存储和检索方法"""
    
    @abstractmethod
    def save_plugin(self, plugin: Plugin) -> bool:
        """保存插件信息
        
        Args:
            plugin: 插件对象
        
        Returns:
            是否成功
        """
        pass
    
    @abstractmethod
    def get_plugin(self, plugin_id: str) -> Optional[Plugin]:
        """获取插件信息
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            插件对象
        """
        pass
    
    @abstractmethod
    def get_all_plugins(self) -> Dict[str, Plugin]:
        """获取所有插件信息
        
        Returns:
            插件ID到插件对象的映射
        """
        pass
    
    @abstractmethod
    def delete_plugin(self, plugin_id: str) -> bool:
        """删除插件信息
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            是否成功
        """
        pass
    
    @abstractmethod
    def save_dependency_group(self, group: DependencyGroup) -> bool:
        """保存依赖组信息
        
        Args:
            group: 依赖组对象
        
        Returns:
            是否成功
        """
        pass
    
    @abstractmethod
    def get_dependency_group(self, group_id: str) -> Optional[DependencyGroup]:
        """获取依赖组信息
        
        Args:
            group_id: 依赖组ID
        
        Returns:
            依赖组对象
        """
        pass
    
    @abstractmethod
    def get_all_dependency_groups(self) -> Dict[str, DependencyGroup]:
        """获取所有依赖组信息
        
        Returns:
            依赖组ID到依赖组对象的映射
        """
        pass
    
    @abstractmethod
    def delete_dependency_group(self, group_id: str) -> bool:
        """删除依赖组信息
        
        Args:
            group_id: 依赖组ID
        
        Returns:
            是否成功
        """
        pass


class JSONStorage(Storage):
    """使用JSON文件实现的存储"""
    
    def __init__(self, data_dir: str):
        """初始化JSON存储
        
        Args:
            data_dir: 数据目录
        """
        self.data_dir = Path(data_dir)
        self.plugins_file = self.data_dir / "plugins.json"
        self.dependency_groups_file = self.data_dir / "dependency_groups.json"
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 初始化文件
        if not self.plugins_file.exists():
            self._save_json(self.plugins_file, {})
        
        if not self.dependency_groups_file.exists():
            self._save_json(self.dependency_groups_file, {})
        
        logger.info(f"初始化JSON存储: 数据目录={self.data_dir}")
    
    def save_plugin(self, plugin: Plugin) -> bool:
        """保存插件信息"""
        plugins = self._load_json(self.plugins_file)
        plugins[plugin.id] = plugin.to_dict()
        return self._save_json(self.plugins_file, plugins)
    
    def get_plugin(self, plugin_id: str) -> Optional[Plugin]:
        """获取插件信息"""
        plugins = self._load_json(self.plugins_file)
        if plugin_id in plugins:
            return Plugin.from_dict(plugins[plugin_id])
        return None
    
    def get_all_plugins(self) -> Dict[str, Plugin]:
        """获取所有插件信息"""
        plugins = self._load_json(self.plugins_file)
        return {pid: Plugin.from_dict(p) for pid, p in plugins.items()}
    
    def delete_plugin(self, plugin_id: str) -> bool:
        """删除插件信息"""
        plugins = self._load_json(self.plugins_file)
        if plugin_id in plugins:
            del plugins[plugin_id]
            return self._save_json(self.plugins_file, plugins)
        return True
    
    def save_dependency_group(self, group: DependencyGroup) -> bool:
        """保存依赖组信息"""
        groups = self._load_json(self.dependency_groups_file)
        groups[group.id] = group.to_dict()
        return self._save_json(self.dependency_groups_file, groups)
    
    def get_dependency_group(self, group_id: str) -> Optional[DependencyGroup]:
        """获取依赖组信息"""
        groups = self._load_json(self.dependency_groups_file)
        if group_id in groups:
            return DependencyGroup.from_dict(groups[group_id])
        return None
    
    def get_all_dependency_groups(self) -> Dict[str, DependencyGroup]:
        """获取所有依赖组信息"""
        groups = self._load_json(self.dependency_groups_file)
        return {gid: DependencyGroup.from_dict(g) for gid, g in groups.items()}
    
    def delete_dependency_group(self, group_id: str) -> bool:
        """删除依赖组信息"""
        groups = self._load_json(self.dependency_groups_file)
        if group_id in groups:
            del groups[group_id]
            return self._save_json(self.dependency_groups_file, groups)
        return True
    
    def _load_json(self, file_path: Path) -> Dict:
        """加载JSON文件"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.exception(f"加载JSON文件失败: {e}")
            return {}
    
    def _save_json(self, file_path: Path, data: Dict) -> bool:
        """保存JSON文件"""
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.exception(f"保存JSON文件失败: {e}")
            return False


class SQLiteStorage(Storage):
    """使用SQLite实现的存储"""
    
    def __init__(self, data_dir: str):
        """初始化SQLite存储
        
        Args:
            data_dir: 数据目录
        """
        self.data_dir = Path(data_dir)
        self.db_file = self.data_dir / "mcp_market.db"
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 初始化数据库
        self._init_db()
        
        logger.info(f"初始化SQLite存储: 数据库文件={self.db_file}")
    
    def _init_db(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 创建插件表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS plugins (
            id TEXT PRIMARY KEY,
            data TEXT NOT NULL
        )
        ''')
        
        # 创建依赖组表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS dependency_groups (
            id TEXT PRIMARY KEY,
            data TEXT NOT NULL
        )
        ''')
        
        conn.commit()
        conn.close()
    
    def save_plugin(self, plugin: Plugin) -> bool:
        """保存插件信息"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 转换为JSON字符串
            data = json.dumps(plugin.to_dict(), ensure_ascii=False)
            
            # 插入或更新
            cursor.execute(
                'INSERT OR REPLACE INTO plugins (id, data) VALUES (?, ?)',
                (plugin.id, data)
            )
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.exception(f"保存插件信息失败: {e}")
            return False
    
    def get_plugin(self, plugin_id: str) -> Optional[Plugin]:
        """获取插件信息"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('SELECT data FROM plugins WHERE id = ?', (plugin_id,))
            row = cursor.fetchone()
            
            conn.close()
            
            if row:
                data = json.loads(row[0])
                return Plugin.from_dict(data)
            
            return None
        except Exception as e:
            logger.exception(f"获取插件信息失败: {e}")
            return None
    
    def get_all_plugins(self) -> Dict[str, Plugin]:
        """获取所有插件信息"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('SELECT id, data FROM plugins')
            rows = cursor.fetchall()
            
            conn.close()
            
            result = {}
            for row in rows:
                plugin_id = row[0]
                data = json.loads(row[1])
                result[plugin_id] = Plugin.from_dict(data)
            
            return result
        except Exception as e:
            logger.exception(f"获取所有插件信息失败: {e}")
            return {}
    
    def delete_plugin(self, plugin_id: str) -> bool:
        """删除插件信息"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM plugins WHERE id = ?', (plugin_id,))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.exception(f"删除插件信息失败: {e}")
            return False
    
    def save_dependency_group(self, group: DependencyGroup) -> bool:
        """保存依赖组信息"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 转换为JSON字符串
            data = json.dumps(group.to_dict(), ensure_ascii=False)
            
            # 插入或更新
            cursor.execute(
                'INSERT OR REPLACE INTO dependency_groups (id, data) VALUES (?, ?)',
                (group.id, data)
            )
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.exception(f"保存依赖组信息失败: {e}")
            return False
    
    def get_dependency_group(self, group_id: str) -> Optional[DependencyGroup]:
        """获取依赖组信息"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('SELECT data FROM dependency_groups WHERE id = ?', (group_id,))
            row = cursor.fetchone()
            
            conn.close()
            
            if row:
                data = json.loads(row[0])
                return DependencyGroup.from_dict(data)
            
            return None
        except Exception as e:
            logger.exception(f"获取依赖组信息失败: {e}")
            return None
    
    def get_all_dependency_groups(self) -> Dict[str, DependencyGroup]:
        """获取所有依赖组信息"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('SELECT id, data FROM dependency_groups')
            rows = cursor.fetchall()
            
            conn.close()
            
            result = {}
            for row in rows:
                group_id = row[0]
                data = json.loads(row[1])
                result[group_id] = DependencyGroup.from_dict(data)
            
            return result
        except Exception as e:
            logger.exception(f"获取所有依赖组信息失败: {e}")
            return {}
    
    def delete_dependency_group(self, group_id: str) -> bool:
        """删除依赖组信息"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM dependency_groups WHERE id = ?', (group_id,))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.exception(f"删除依赖组信息失败: {e}")
            return False


def create_storage(storage_type: str, data_dir: str) -> Storage:
    """创建存储实例
    
    Args:
        storage_type: 存储类型，支持"json"和"sqlite"
        data_dir: 数据目录
    
    Returns:
        存储实例
    """
    if storage_type.lower() == "json":
        return JSONStorage(data_dir)
    elif storage_type.lower() == "sqlite":
        return SQLiteStorage(data_dir)
    else:
        logger.warning(f"未知的存储类型: {storage_type}，使用SQLite作为默认存储")
        return SQLiteStorage(data_dir) 