"""
Plugin manager for MCP Market application
"""
import os
import logging
import json
from typing import List, Dict, Any, Optional
from .models import Plugin, Server

logger = logging.getLogger(__name__)

class PluginManager:
    """Manager for MCP plugins"""
    
    def __init__(self, data_dir: str = None):
        """
        Initialize the plugin manager
        
        Args:
            data_dir: Directory to store plugin data
        """
        self.data_dir = data_dir or os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "mcp_market_data")
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        self.plugins_file = os.path.join(self.data_dir, "plugins.json")
        self.plugins = self._load_plugins()
    
    def _load_plugins(self) -> Dict[str, Plugin]:
        """
        Load plugins from file
        
        Returns:
            Dictionary of plugins
        """
        if not os.path.exists(self.plugins_file):
            return {}
        
        try:
            with open(self.plugins_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                return {p["id"]: Plugin(**p) for p in data}
        except Exception as e:
            logger.error(f"Error loading plugins: {e}")
            return {}
    
    def _save_plugins(self):
        """Save plugins to file"""
        try:
            with open(self.plugins_file, "w", encoding="utf-8") as f:
                json.dump([p.dict() for p in self.plugins.values()], f, indent=2)
        except Exception as e:
            logger.error(f"Error saving plugins: {e}")
    
    def get_plugins(self, server_id: Optional[str] = None) -> List[Plugin]:
        """
        Get all plugins or plugins for a specific server
        
        Args:
            server_id: Server ID to filter by
            
        Returns:
            List of plugins
        """
        if server_id:
            return [p for p in self.plugins.values() if p.server_id == server_id]
        return list(self.plugins.values())
    
    def get_plugin(self, plugin_id: str) -> Optional[Plugin]:
        """
        Get a plugin by ID
        
        Args:
            plugin_id: Plugin ID
            
        Returns:
            Plugin if found, None otherwise
        """
        return self.plugins.get(plugin_id)
    
    def add_plugin(self, plugin: Plugin) -> Plugin:
        """
        Add a new plugin
        
        Args:
            plugin: Plugin to add
            
        Returns:
            Added plugin
        """
        self.plugins[plugin.id] = plugin
        self._save_plugins()
        return plugin
    
    def update_plugin(self, plugin_id: str, plugin_data: Dict[str, Any]) -> Optional[Plugin]:
        """
        Update a plugin
        
        Args:
            plugin_id: Plugin ID
            plugin_data: Updated plugin data
            
        Returns:
            Updated plugin if found, None otherwise
        """
        if plugin_id not in self.plugins:
            return None
        
        plugin = self.plugins[plugin_id]
        for key, value in plugin_data.items():
            if hasattr(plugin, key):
                setattr(plugin, key, value)
        
        self._save_plugins()
        return plugin
    
    def delete_plugin(self, plugin_id: str) -> bool:
        """
        Delete a plugin
        
        Args:
            plugin_id: Plugin ID
            
        Returns:
            True if deleted, False otherwise
        """
        if plugin_id not in self.plugins:
            return False
        
        del self.plugins[plugin_id]
        self._save_plugins()
        return True 