"""
API routes for the AI Assistant and MCP Marketplace
"""
from fastapi import <PERSON>AP<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
import logging

# Import specific API modules
from .ai_chat import setup_ai_chat_api
from .mcp_market import setup_mcp_market_api

logger = logging.getLogger(__name__)

def setup_api_routes(app: FastAPI):
    """
    Setup API routes for the application
    
    Args:
        app: FastAPI application
    """
    # Register API endpoints from specific modules
    setup_ai_chat_api(app)
    setup_mcp_market_api(app)
    
    # API status endpoint
    @app.get("/api/status")
    async def get_status():
        """
        Get application status
        """
        return {
            "status": "ok",
            "version": "1.0.0",
            "services": {
                "ai_chat": "available",
                "mcp_market": "available"
            }
        } 