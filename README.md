# AI Assistant & MCP Marketplace

一个集成了AI助手和MCP插件市场的统一应用平台。

## 功能特性

### 🤖 AI对话助手
- 支持多种AI模型（DeepSeek、Qwen等）
- 智能对话和会话管理
- 消息历史记录
- 实时响应界面

### 🔌 MCP插件市场
- 插件发现和安装
- 插件生命周期管理
- 插件进程管理
- 插件仓库管理

### 📚 知识库管理
- 文档上传和处理
- 向量化存储
- 智能检索
- 上下文增强

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置设置

复制示例配置文件并修改：

```bash
cp example_config.yaml config.yaml
```

编辑 `config.yaml` 文件，添加你的API密钥和配置。

### 3. 启动应用

#### 开发环境
```bash
python main.py --env dev --debug
```

#### 生产环境
```bash
python main.py --env prod
```

#### 使用启动脚本
```bash
python start.py
```

### 4. 访问应用

- 主页: http://localhost:8081/
- AI对话: http://localhost:8081/ai-chat/
- MCP市场: http://localhost:8081/mcp-market/

## 项目结构

```
run-mcp/
├── core/                   # 核心功能模块
├── apps/                   # 应用模块
│   ├── ai_chat/           # AI对话应用
│   └── mcp_market/        # MCP插件市场
├── api/                   # API接口
├── utils/                 # 通用工具函数
├── templates/             # 公共模板
├── static/                # 公共静态资源
├── config/                # 配置文件
├── kb_documents/          # 知识库文档
├── vectorstore_db/        # 向量数据库
└── mcp_market_data/       # MCP市场数据
```

详细的项目结构说明请参考 [project_structure.md](project_structure.md)。

## 配置说明

### AI模型配置

在 `config.yaml` 中配置AI模型：

```yaml
models:
  deepseek:
    api_key: "your-api-key"
    api_url: "https://api.deepseek.com/v1"
    model_name: "deepseek-chat"
    provider: "deepseek"
```

### MCP服务器配置

```yaml
mcp_servers:
  server_name:
    connection_type: "sse"  # 或 "stdio"
    url: "http://localhost:8082"
    headers:
      Accept: "text/event-stream"
```

## 开发指南

### 添加新的AI模型

1. 在 `config/` 目录下的配置文件中添加模型配置
2. 在 `apps/ai_chat/models.py` 中定义模型类
3. 在 `apps/ai_chat/services.py` 中实现模型调用逻辑

### 添加新的MCP插件

1. 将插件放置在 `mcp_market_data/plugins/` 目录
2. 在MCP市场界面中管理插件
3. 使用API接口调用插件功能

### 扩展知识库

1. 将文档放置在 `kb_documents/` 目录
2. 使用知识库管理工具处理文档
3. 通过API接口查询知识库

## 依赖项

主要依赖包括：

- FastAPI: Web框架
- Uvicorn: ASGI服务器
- Jinja2: 模板引擎
- Pydantic: 数据验证
- aiohttp: 异步HTTP客户端
- langchain: 语言模型工具链
- chromadb: 向量数据库

完整的依赖列表请参考 `requirements.txt`。

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 支持

如果遇到问题，请查看：

1. [项目结构文档](project_structure.md)
2. 配置示例文件
3. 日志文件（`app.log`, `mcp_market.log`）

或者提交Issue获取帮助。
