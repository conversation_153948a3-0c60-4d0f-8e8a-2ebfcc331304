"""
Standard MCP client implementation using the official MCP library
"""
import logging
import traceback
from typing import Dict, List, Any
import pandas as pd

logger = logging.getLogger(__name__)

# Import the MCP client library
try:
    from mcp import ClientSession, StdioServerParameters, types
    from mcp.client.stdio import stdio_client
    from mcp.client.sse import sse_client
    MCP_STANDARD_AVAILABLE = True
except ImportError:
    logger.warning("Standard MCP client libraries not available - you may need to install them with 'pip install mcp-client'")
    MCP_STANDARD_AVAILABLE = False

# Alternative implementation using requests when httpx fails
def use_requests_fallback():
    """Try to use requests as a fallback when httpx isn't available"""
    try:
        import requests
        return True
    except ImportError:
        logger.warning("requests library not available. Some fallback functionality will be limited.")
        return False

REQUESTS_AVAILABLE = use_requests_fallback()

class StandardMCPClient:
    """Implementation using the standard MCP client library with correct endpoint usage"""
    
    def __init__(self, server_name: str, server_config: Dict[str, Any]):
        """Initialize the MCP client with server configuration"""
        self.server_name = server_name
        self.config = server_config
        self.connection_type = server_config.get("connection_type", "stdio")
        self.client_session = None
        
        # Set up parameters based on connection type
        if self.connection_type == "stdio":
            # Get the command and args for stdio connection
            command = server_config.get("command", "")
            args = server_config.get("args", [])
            
            if not command:
                raise ValueError("No command specified for stdio connection")
                
            # Create stdio server parameters
            self.server_params = StdioServerParameters(
                command=command,
                args=args,
                env=server_config.get("env", {}),
                cwd=server_config.get("cwd")
            )
            
        elif self.connection_type in ["sse", "streamableHttp"]:
            # Get URL for SSE connection
            url = server_config.get("url", "")
            if not url:
                raise ValueError("No URL specified for SSE connection")
                
            # Clean up URL and ensure no trailing slash
            self.base_url = url.rstrip("/")
            if self.base_url.endswith("/sse"):
                self.base_url = self.base_url[:-4]
                
            # Define specific endpoints
            self.sse_url = f"{self.base_url}/sse"
        else:
            raise ValueError(f"Unsupported connection type: {self.connection_type}")
            
        # Get headers and timeout
        self.headers = server_config.get("headers", {})
        self.timeout = server_config.get("timeout", 30)
    
    async def initialize_session(self):
        """Initialize the client session based on connection type"""
        if not MCP_STANDARD_AVAILABLE:
            raise ImportError("MCP client library not available")
            
        try:
            if self.connection_type == "stdio":
                # Use stdio client
                logger.info(f"Initializing stdio client for {self.server_name}")
                read_stream, write_stream = await stdio_client(self.server_params)
            else:
                # Use SSE client
                logger.info(f"Initializing SSE client for {self.server_name} at {self.sse_url}")
                # Ensure proper headers for SSE
                sse_headers = self.headers.copy()
                if "Accept" not in sse_headers:
                    sse_headers["Accept"] = "text/event-stream"
                    
                read_stream, write_stream = await sse_client(self.sse_url, headers=sse_headers)
                
            # Create client session
            session = ClientSession(read_stream, write_stream)
            # Initialize session
            await session.initialize()
            
            return session
            
        except Exception as e:
            logger.error(f"Failed to initialize MCP client session: {e}")
            raise
    
    async def get_tools(self) -> List[Dict[str, Any]]:
        """Get the list of tools using the official MCP client library"""
        if not MCP_STANDARD_AVAILABLE:
            logger.error("Cannot get tools: MCP client library not available")
            return []
        try:
            if self.connection_type == "stdio":
                # Use stdio client
                async with stdio_client(self.server_params) as (read_stream, write_stream):
                    async with ClientSession(read_stream, write_stream) as session:
                        logger.info(f"Getting tools from stdio server {self.server_name}")
                        # Initialize session
                        await session.initialize()
                        # Get tools
                        tools_result = await session.list_tools()
            else:
                # Use SSE client
                logger.info(f"Getting tools from SSE server {self.server_name}")
                # Ensure proper headers for SSE
                sse_headers = self.headers.copy()
                if "Accept" not in sse_headers:
                    sse_headers["Accept"] = "text/event-stream"

                async with sse_client(self.sse_url, headers=sse_headers) as (read_stream, write_stream):
                    async with ClientSession(read_stream, write_stream) as session:
                        # Initialize session
                        await session.initialize()
                        # Get tools
                        tools_result = await session.list_tools()

            # Convert tools to our standard format
            tools = []
            for tool in tools_result.tools:
                tools.append({
                    "name": tool.name,
                    "description": tool.description,
                    "params": tool.inputSchema
                })

            logger.info(f"Got {len(tools)} tools from {self.server_name}")
            return tools

        except Exception as e:
            logger.error(f"Error getting tools: {e}")
            # Log detailed traceback for debugging
            logger.debug(traceback.format_exc())
            return []
    
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool using the official MCP client library"""
        if not MCP_STANDARD_AVAILABLE:
            logger.error("Cannot call tool: MCP client library not available")
            return {"error": "MCP client library not available"}
            
        try:
            if self.connection_type == "stdio":
                # Use stdio client
                async with stdio_client(self.server_params) as (read_stream, write_stream):
                    async with ClientSession(read_stream, write_stream) as session:
                        logger.info(f"Calling tool {tool_name} on stdio server {self.server_name}")
                        # Initialize session
                        await session.initialize()
                        # Call tool
                        result = await session.call_tool(name=tool_name, arguments=params)
            else:
                # Use SSE client
                logger.info(f"Calling tool {tool_name} on SSE server {self.server_name}")
                # Ensure proper headers for SSE
                sse_headers = self.headers.copy()
                if "Accept" not in sse_headers:
                    sse_headers["Accept"] = "text/event-stream"
                    
                async with sse_client(self.sse_url, headers=sse_headers) as (read_stream, write_stream):
                    async with ClientSession(read_stream, write_stream) as session:
                        # Initialize session
                        await session.initialize()
                        # Call tool
                        result = await session.call_tool(name=tool_name, arguments=params)
            df = pd.DataFrame(result.content)
            df_limited = df.head(100)
            # Return the result
            return df_limited.to_dict('records')
            
        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}")
            # Log detailed traceback for debugging
            logger.debug(traceback.format_exc())
            return {"error": f"工具调用失败: {str(e)}"}

# Client factory function
def create_standard_client(server_name: str, server_config: Dict[str, Any]) -> StandardMCPClient:
    """Create a standard MCP client for the given server configuration"""
    if not MCP_STANDARD_AVAILABLE:
        raise ImportError("MCP client library not available. Please install with 'pip install mcp-client'")
        
    return StandardMCPClient(server_name, server_config) 