# AI Assistant & MCP Marketplace - 模块化项目结构

本项目采用模块化设计，将AI助手和MCP插件市场整合为统一的应用。

## 当前项目结构

```
run-mcp/
│
├── core/                   # 核心功能模块
│   ├── __init__.py
│   ├── app.py              # 核心应用初始化
│   ├── config.py           # 配置管理
│   ├── logger.py           # 日志配置
│   └── errors.py           # 错误处理
│
├── apps/                   # 应用模块
│   ├── __init__.py
│   ├── ai_chat/            # AI对话应用
│   │   ├── __init__.py
│   │   ├── app.py          # AI对话应用初始化
│   │   ├── routes.py       # 路由定义
│   │   ├── models.py       # 数据模型
│   │   ├── services.py     # 业务逻辑
│   │   └── static/         # 静态资源
│   │       ├── css/
│   │       ├── js/
│   │       └── img/
│   │
│   └── mcp_market/         # MCP插件市场
│       ├── __init__.py
│       ├── app.py          # 插件市场应用初始化
│       ├── routes.py       # 路由定义
│       ├── models.py       # 数据模型
│       ├── plugin_manager.py # 插件管理
│       ├── repository.py   # 仓库管理
│       ├── process_manager.py # 进程管理
│       └── static/         # 静态资源
│           ├── css/
│           ├── js/
│           └── img/
│
├── api/                    # API接口
│   ├── __init__.py
│   ├── routes.py           # API路由定义
│   ├── ai_chat.py          # AI对话API
│   └── mcp_market.py       # MCP市场API
│
├── utils/                  # 通用工具函数
│   ├── __init__.py
│   ├── http.py             # HTTP工具
│   ├── file.py             # 文件处理
│   ├── security.py         # 安全工具
│   ├── mcp_client.py       # MCP客户端工具
│   ├── sse_client.py       # SSE客户端工具
│   └── knowledge_base.py   # 知识库管理工具
│
├── templates/              # 公共模板
│   ├── base.html           # 基础模板
│   ├── index.html          # 首页模板
│   ├── knowledge_base.html # 知识库模板
│   └── error.html          # 错误页模板
│
├── static/                 # 公共静态资源
│   ├── css/
│   │   ├── main.css
│   │   └── style.css
│   ├── js/
│   │   ├── main.js
│   │   ├── chat.js
│   │   └── plugin_main.js
│   ├── img/
│   └── index.html
│
├── config/                 # 配置文件
│   ├── __init__.py
│   ├── default.py          # 默认配置
│   ├── development.py      # 开发环境配置
│   └── production.py       # 生产环境配置
│
├── script/                 # 脚本文件
│   ├── clean_plugins.py    # 清理插件脚本
│   └── clear_tables.py     # 清理数据表脚本
│
├── kb_documents/           # 知识库文档目录
├── vectorstore_db/         # 向量数据库目录
├── mcp_market_data/        # MCP市场数据目录
│
├── __init__.py             # 包初始化
├── app.py                  # 主应用入口
├── main.py                 # 主程序入口
├── start.py                # 启动脚本
├── requirements.txt        # 依赖管理
└── example_config.yaml     # 配置示例文件
```

## 模块说明

### 1. core - 核心模块
包含项目的核心功能，如应用初始化、配置管理、日志设置、错误处理等。
- `app.py`: 核心应用初始化和配置
- `config.py`: 配置管理
- `logger.py`: 日志配置
- `errors.py`: 错误处理

### 2. apps - 应用模块
包含项目的各个应用，每个应用都是独立的子模块：
- `ai_chat/`: AI对话应用，提供智能对话功能
- `mcp_market/`: MCP插件市场，管理和运行MCP插件

### 3. api - API接口
统一管理项目的API接口，为前端和其他服务提供数据：
- `routes.py`: 主要API路由定义
- `ai_chat.py`: AI对话相关API
- `mcp_market.py`: MCP市场相关API

### 4. utils - 工具模块
提供通用的工具函数和类，供其他模块使用：
- `http.py`: HTTP工具函数
- `file.py`: 文件处理工具
- `security.py`: 安全相关工具
- `mcp_client.py`: MCP客户端工具
- `sse_client.py`: SSE客户端工具
- `knowledge_base.py`: 知识库管理工具

### 5. templates - 公共模板
存放公共的HTML模板文件，使用Jinja2模板引擎。

### 6. static - 公共静态资源
存放公共的CSS、JavaScript和图片等静态资源。

### 7. config - 配置文件
管理不同环境的配置文件：
- `default.py`: 默认配置
- `development.py`: 开发环境配置
- `production.py`: 生产环境配置

## 应用特性

### AI对话应用 (ai_chat)
- 支持多种AI模型
- 会话管理
- 消息历史记录
- 实时对话界面

### MCP插件市场 (mcp_market)
- 插件发现和安装
- 插件生命周期管理
- 插件进程管理
- 插件仓库管理

## 启动方式

### 开发环境
```bash
python main.py --env dev --debug
```

### 生产环境
```bash
python main.py --env prod
```

### 使用启动脚本
```bash
python start.py
```

## 访问地址

- 主页: http://localhost:8081/
- AI对话: http://localhost:8081/ai-chat/
- MCP市场: http://localhost:8081/mcp-market/

## 模块化设计优势

1. **关注点分离**：每个模块负责特定的功能，减少耦合
2. **代码重用**：通用功能被抽象到独立模块，便于复用
3. **可维护性**：模块化结构使代码更易于理解和维护
4. **可扩展性**：新功能可以作为新模块添加，不影响现有代码
5. **测试友好**：独立模块更容易进行单元测试
6. **部署灵活**：可以独立部署不同的应用模块
