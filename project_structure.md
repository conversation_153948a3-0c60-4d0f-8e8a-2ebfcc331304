# 模块化项目结构

参考开源项目组织方式，我们将项目重构为以下模块化结构：

```
chat/
│
├── core/                   # 核心功能模块
│   ├── __init__.py
│   ├── app.py              # 核心应用初始化
│   ├── config.py           # 配置管理
│   ├── logger.py           # 日志配置
│   └── errors.py           # 错误处理
│
├── apps/                   # 应用模块
│   ├── __init__.py
│   ├── ai_chat/            # AI对话应用
│   │   ├── __init__.py
│   │   ├── app.py          # AI对话应用初始化
│   │   ├── routes.py       # 路由定义
│   │   ├── models.py       # 数据模型
│   │   ├── services.py     # 业务逻辑
│   │   └── static/         # 静态资源
│   │       ├── css/
│   │       ├── js/
│   │       └── img/
│   │
│   └── mcp_market/         # MCP插件市场
│       ├── __init__.py
│       ├── app.py          # 插件市场应用初始化
│       ├── routes.py       # 路由定义
│       ├── models.py       # 数据模型
│       ├── plugin_manager.py # 插件管理
│       ├── repository.py   # 仓库管理
│       ├── process_manager.py # 进程管理
│       └── static/         # 静态资源
│           ├── css/
│           ├── js/
│           └── img/
│
├── api/                    # API接口
│   ├── __init__.py
│   ├── routes.py           # API路由定义
│   ├── models.py           # API数据模型
│   ├── ai_chat.py          # AI对话API
│   └── mcp_market.py       # MCP市场API
│
├── utils/                  # 通用工具函数
│   ├── __init__.py
│   ├── http.py             # HTTP工具
│   ├── file.py             # 文件处理
│   └── security.py         # 安全工具
│
├── templates/              # 公共模板
│   ├── base.html           # 基础模板
│   ├── index.html          # 首页模板
│   └── error.html          # 错误页模板
│
├── static/                 # 公共静态资源
│   ├── css/
│   ├── js/
│   └── img/
│
├── config/                 # 配置文件
│   ├── __init__.py
│   ├── default.py          # 默认配置
│   ├── development.py      # 开发环境配置
│   └── production.py       # 生产环境配置
│
├── __init__.py             # 包初始化
├── app.py                  # 主应用入口
├── main.py                 # 主程序入口
└── requirements.txt        # 依赖管理
```

## 模块说明

### 1. core - 核心模块
包含项目的核心功能，如应用初始化、配置管理、日志设置等。

### 2. apps - 应用模块
包含项目的各个应用，如AI对话和MCP插件市场，每个应用都是独立的子模块。

### 3. api - API接口
统一管理项目的API接口，为前端和其他服务提供数据。

### 4. utils - 工具模块
提供通用的工具函数和类，供其他模块使用。

### 5. templates - 公共模板
存放公共的HTML模板文件。

### 6. static - 公共静态资源
存放公共的CSS、JavaScript和图片等静态资源。

### 7. config - 配置文件
管理不同环境的配置文件。

## 模块化设计优势

1. **关注点分离**：每个模块负责特定的功能，减少耦合。
2. **代码重用**：通用功能被抽象到独立模块，便于复用。
3. **可维护性**：模块化结构使代码更易于理解和维护。
4. **可扩展性**：新功能可以作为新模块添加，不影响现有代码。
5. **测试友好**：独立模块更容易进行单元测试。

## 后续步骤

1. 实现核心模块
2. 重构AI对话应用和MCP插件市场为独立模块
3. 统一API接口设计
4. 实现公共工具模块
5. 配置统一的模板和静态资源管理 
