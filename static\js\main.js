// 全局变量
let models = {};
let mcpServers = {};
let chatHistory = [];
let providerConfigs = {};
let mcpExamples = {};
let mcpServerHealth = {};
let isDarkMode = false;
let agents = {}; // 存储智能体配置
let currentAgentThinking = null; // 当前智能体的思考过程

// 预置MCP服务器配置示例
const MCP_CONFIG_EXAMPLES = {
    'fetch': {
        name: 'fetch',
        description: 'Fetch content at URLs from the world wide web',
        connection_type: 'stdio',
        command: 'uvx',
        args: ['mcp-server-fetch']
    },
    'filesystem': {
        name: 'filesystem',
        description: 'File system operations with security restrictions',
        connection_type: 'stdio',
        command: 'uvx',
        args: ['mcp-server-filesystem', '--allowed-dirs', '.']
    },
    'sqlite': {
        name: 'sqlite',
        description: 'SQLite database operations',
        connection_type: 'stdio',
        command: 'uvx',
        args: ['mcp-server-sqlite', '--db-path', './data.db']
    },
    'akshare': {
        name: 'akshare-one-mcp',
        description: 'AKShare中国金融市场数据服务 (stdio连接)',
        connection_type: 'stdio',
        command: 'uvx',
        args: ['akshare-one-mcp']
    },
    'http-api': {
        name: 'example-api',
        description: 'Example HTTP API service',
        connection_type: 'sse',
        url: 'https://api.example.com/mcp',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer your-token-here'
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化...');
    
    // 检查所有必要的页面区域是否存在
    const requiredElements = [
        'chatContainer', 
        'modelConfigArea', 
        'mcpConfigArea', 
        'agentConfigArea', 
        'settingsArea'
    ];
    
    let missingElements = [];
    
    requiredElements.forEach(id => {
        const element = document.getElementById(id);
        if (!element) {
            console.error(`必要的页面元素不存在: ${id}`);
            missingElements.push(id);
        }
    });
    
    if (missingElements.length > 0) {
        console.error('页面缺少必要元素:', missingElements);
        showNotification('页面初始化错误: 某些UI组件缺失', 'error');
    }
    
    try {
        // 加载数据
        loadProviders();
        loadMCPExamples();
        loadModels();
        loadMCPServers();
        loadChatHistory();
        loadMCPServerHealth();
        loadAgents(); // 加载智能体
        loadSettings(); // 加载用户设置
        
        // 初始化主题
        initTheme();
        
        // 确保默认显示聊天页面
        switchToPage('chatContainer', 'chat');
        
        // 更新当前API密钥
        updateCurrentApiKey();
        
        // 每5分钟自动检查一次健康状态
        setInterval(loadMCPServerHealth, 5 * 60 * 1000);
        
        // 添加动画效果
        addAnimationEffects();
        
        // 绑定温度滑块事件
        const tempSlider = document.getElementById('agentTemperature');
        if (tempSlider) {
            tempSlider.addEventListener('input', updateTemperatureValue);
        }
        
        console.log('页面初始化完成');
    } catch (error) {
        console.error('页面初始化过程中出错:', error);
        showNotification('初始化失败: ' + error.message, 'error');
    }
});

// 初始化主题
function initTheme() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        enableDarkMode();
    }
    
    // 添加主题切换按钮事件
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
}

// 切换主题
function toggleTheme() {
    if (isDarkMode) {
        disableDarkMode();
    } else {
        enableDarkMode();
    }
}

// 启用暗色模式
function enableDarkMode() {
    document.body.classList.add('dark-theme');
    localStorage.setItem('theme', 'dark');
    isDarkMode = true;
    
    // 更新图标
    const themeIcon = document.querySelector('#themeToggle i');
    if (themeIcon) {
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
    }
}

// 禁用暗色模式
function disableDarkMode() {
    document.body.classList.remove('dark-theme');
    localStorage.setItem('theme', 'light');
    isDarkMode = false;
    
    // 更新图标
    const themeIcon = document.querySelector('#themeToggle i');
    if (themeIcon) {
        themeIcon.classList.remove('fa-sun');
        themeIcon.classList.add('fa-moon');
    }
}

// 添加动画效果
function addAnimationEffects() {
    // 为卡片添加淡入效果
    const cards = document.querySelectorAll('.config-item, .message');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 + index * 50);
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // 添加图标
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    if (type === 'warning') icon = 'exclamation-triangle';
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${icon} mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 添加入场动画
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // 自动消失
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// 加载提供商配置
async function loadProviders() {
    try {
        const response = await fetch('/api/providers');
        providerConfigs = await response.json();
    } catch (error) {
        console.error('加载提供商配置失败:', error);
    }
}

// 加载MCP示例配置
async function loadMCPExamples() {
    try {
        const response = await fetch('/api/mcp-examples');
        mcpExamples = await response.json();
    } catch (error) {
        console.error('加载MCP示例失败:', error);
    }
}

// 加载MCP服务器健康状态
async function loadMCPServerHealth() {
    try {
        const response = await fetch('/api/mcp-servers/health');
        mcpServerHealth = await response.json();
        renderMCPServerList(); // 重新渲染以显示健康状态
        updateMCPServerCheckboxes(); // 更新复选框状态
    } catch (error) {
        console.error('加载MCP服务器健康状态失败:', error);
    }
}

// 手动检查所有MCP服务器健康状态
async function checkAllMCPHealth() {
    const indicator = document.getElementById('globalHealthCheckIndicator');
    if (indicator) {
        indicator.classList.remove('hidden');
    }
    
    try {
        const response = await fetch('/api/mcp-servers/health-check', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ server_names: [] })
        });
        
        if (response.ok) {
            mcpServerHealth = await response.json();
            renderMCPServerList();
            updateMCPServerCheckboxes();
            showNotification('健康检查完成', 'success');
        } else {
            showNotification('健康检查失败', 'error');
        }
    } catch (error) {
        showNotification('健康检查失败: ' + error.message, 'error');
    } finally {
        if (indicator) {
            indicator.classList.add('hidden');
        }
    }
}

// 检查单个MCP服务器健康状态
async function checkSingleMCPHealth(serverName) {
    try {
        // 显示健康检查指示器
        const indicator = document.getElementById('globalHealthCheckIndicator');
        if (indicator) {
            indicator.classList.remove('hidden');
        }
        
        const response = await fetch('/api/mcp-servers/health-check', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ server_names: [serverName] })
        });
        
        if (response.ok) {
            const result = await response.json();
            mcpServerHealth[serverName] = result[serverName];
            renderMCPServerList();
            updateMCPServerCheckboxes();
            showNotification(`已更新 ${serverName} 的健康状态`, 'success');
        }
    } catch (error) {
        console.error(`检查服务器 ${serverName} 健康状态失败:`, error);
        showNotification(`检查服务器 ${serverName} 健康状态失败: ${error.message}`, 'error');
    } finally {
        // 隐藏健康检查指示器
        const indicator = document.getElementById('globalHealthCheckIndicator');
        if (indicator) {
            indicator.classList.add('hidden');
        }
    }
}

// 获取健康状态图标和文本
function getHealthStatusDisplay(status) {
    switch (status) {
        case 'healthy':
            return { icon: 'fas fa-check-circle', text: '健康', class: 'healthy' };
        case 'warning':
            return { icon: 'fas fa-exclamation-triangle', text: '警告', class: 'warning' };
        case 'error':
            return { icon: 'fas fa-times-circle', text: '错误', class: 'error' };
        default:
            return { icon: 'fas fa-question-circle', text: '未知', class: 'unknown' };
    }
}

// 格式化时间显示
function formatTime(isoString) {
    if (!isoString) return '未知';
    const date = new Date(isoString);
    return date.toLocaleTimeString();
}

// 切换MCP配置类型
function toggleMCPConfigType() {
    const configType = document.querySelector('input[name="mcpConfigType"]:checked').value;
    const simpleConfig = document.getElementById('simpleConfig');
    const jsonConfig = document.getElementById('jsonConfig');
    
    if (configType === 'json') {
        simpleConfig.classList.add('hidden');
        jsonConfig.classList.remove('hidden');
    } else {
        simpleConfig.classList.remove('hidden');
        jsonConfig.classList.add('hidden');
    }
}

// 切换MCP连接类型字段显示
function toggleMCPConnectionFields() {
    const connectionType = document.getElementById('mcpConnectionType').value;
    const stdioConfig = document.getElementById('stdioConfig');
    const httpConfig = document.getElementById('httpConfig');
    
    if (connectionType === 'stdio') {
        stdioConfig.classList.remove('hidden');
        httpConfig.classList.add('hidden');
    } else {
        stdioConfig.classList.add('hidden');
        httpConfig.classList.remove('hidden');
    }
}

// 添加环境变量行
function addEnvVar() {
    const container = document.getElementById('envVarsContainer');
    const envRow = document.createElement('div');
    envRow.className = 'env-row flex space-x-2';
    envRow.innerHTML = `
        <input type="text" placeholder="变量名" 
               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        <input type="text" placeholder="变量值" 
               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        <button type="button" onclick="removeEnvVar(this)" 
                class="text-red-600 hover:text-red-800 px-2">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(envRow);
}

// 移除环境变量行
function removeEnvVar(button) {
    button.closest('.env-row').remove();
}

// 添加请求头行
function addHeaderRow() {
    const container = document.getElementById('headersContainer');
    const headerRow = document.createElement('div');
    headerRow.className = 'header-row flex space-x-2';
    headerRow.innerHTML = `
        <input type="text" placeholder="请求头名称" 
               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        <input type="text" placeholder="请求头值" 
               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        <button type="button" onclick="removeHeaderRow(this)" 
                class="text-red-600 hover:text-red-800 px-2">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(headerRow);
}

// 移除请求头行
function removeHeaderRow(button) {
    button.closest('.header-row').remove();
}

// 加载MCP配置示例
function loadMCPExample(exampleType) {
    const example = MCP_CONFIG_EXAMPLES[exampleType];
    if (!example) return;
    
    // 基本信息
    document.getElementById('mcpName').value = example.name;
    document.getElementById('mcpDescription').value = example.description;
    document.getElementById('mcpConnectionType').value = example.connection_type;
    
    // 切换连接类型字段
    toggleMCPConnectionFields();
    
    if (example.connection_type === 'stdio') {
        // stdio 配置
        document.getElementById('mcpCommand').value = example.command || '';
        document.getElementById('mcpArgs').value = (example.args || []).join(', ');
        
        // 清空环境变量并添加示例
        const envContainer = document.getElementById('envVarsContainer');
        envContainer.innerHTML = '';
        if (example.env) {
            Object.entries(example.env).forEach(([key, value]) => {
                addEnvVar();
                const envRows = envContainer.querySelectorAll('.env-row');
                const lastRow = envRows[envRows.length - 1];
                lastRow.querySelector('input:first-child').value = key;
                lastRow.querySelector('input:nth-child(2)').value = value;
            });
        }
    } else {
        // HTTP 配置
        document.getElementById('mcpUrl').value = example.url || '';
        
        // 清空请求头并添加示例
        const headersContainer = document.getElementById('headersContainer');
        headersContainer.innerHTML = '';
        if (example.headers) {
            Object.entries(example.headers).forEach(([key, value]) => {
                addHeaderRow();
                const headerRows = headersContainer.querySelectorAll('.header-row');
                const lastRow = headerRows[headerRows.length - 1];
                lastRow.querySelector('input:first-child').value = key;
                lastRow.querySelector('input:nth-child(2)').value = value;
            });
        }
    }
}

// 收集环境变量
function collectEnvVars() {
    const envVars = {};
    const envRows = document.querySelectorAll('#envVarsContainer .env-row');
    envRows.forEach(row => {
        const key = row.querySelector('input:first-child').value.trim();
        const value = row.querySelector('input:nth-child(2)').value.trim();
        if (key && value) {
            envVars[key] = value;
        }
    });
    return envVars;
}

// 收集请求头
function collectHeaders() {
    const headers = {};
    const headerRows = document.querySelectorAll('#headersContainer .header-row');
    headerRows.forEach(row => {
        const key = row.querySelector('input:first-child').value.trim();
        const value = row.querySelector('input:nth-child(2)').value.trim();
        if (key && value) {
            headers[key] = value;
        }
    });
    return headers;
}

// 更新提供商默认设置
function updateProviderDefaults() {
    const provider = document.getElementById('modelProvider').value;
    const apiUrlInput = document.getElementById('modelApiUrl');
    const modelNameInput = document.getElementById('modelModelName');
    const providerInfo = document.getElementById('providerInfo');
    const providerDetails = document.getElementById('providerDetails');
    
    if (provider && providerConfigs[provider]) {
        const config = providerConfigs[provider];
        
        // 设置默认URL（用户可以修改）
        apiUrlInput.placeholder = `API地址 (默认: ${config.default_url})`;
        
        // 显示可用模型建议
        modelNameInput.placeholder = `模型名称 (如: ${config.default_models.join(', ')})`;
        
        // 显示提供商信息
        providerDetails.innerHTML = `
            <div><strong>名称:</strong> ${config.name}</div>
            <div><strong>默认URL:</strong> ${config.default_url}</div>
            <div><strong>推荐模型:</strong> ${config.default_models.join(', ')}</div>
        `;
        providerInfo.classList.remove('hidden');
    } else {
        apiUrlInput.placeholder = 'API地址 (留空使用默认)';
        modelNameInput.placeholder = '具体模型名称';
        providerInfo.classList.add('hidden');
    }
}

// 加载模型配置
async function loadModels() {
    try {
        console.log('开始加载模型列表...');
        const response = await fetch('/api/models');
        
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }
        
        models = await response.json();
        console.log('成功获取模型列表:', Object.keys(models));
        
        // 检查DOM元素存在
        const modelList = document.getElementById('modelList');
        if (!modelList) {
            console.error('modelList元素不存在');
            showNotification('无法渲染模型列表：界面元素不存在', 'error');
        } else {
            renderModelList();
        }
        
        updateModelSelect();
    } catch (error) {
        console.error('加载模型配置失败:', error);
        showNotification('加载模型配置失败: ' + error.message, 'error');
    }
}

// 渲染模型列表
function renderModelList() {
    try {
        const modelList = document.getElementById('modelList');
        if (!modelList) {
            console.error('无法找到modelList元素');
            return;
        }
        
        modelList.innerHTML = '';
        
        if (Object.keys(models).length === 0) {
            modelList.innerHTML = '<div class="text-center p-4 text-gray-500">暂无模型配置</div>';
            return;
        }
        
        Object.entries(models).forEach(([name, config]) => {
            const item = document.createElement('div');
            item.className = 'config-item';
            
            const providerName = providerConfigs[config.provider]?.name || config.provider;
            const apiUrl = config.api_url || '默认';
            const modelName = config.model_name || name;
            
            item.innerHTML = `
                <div class="info">
                    <div class="name">${name}</div>
                    <div class="details">提供商: ${providerName}</div>
                    <div class="details">模型: ${modelName}</div>
                    <div class="details">API: ${apiUrl}</div>
                </div>
                <div class="actions">
                    <button class="btn-sm btn-danger" onclick="deleteModel('${name}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            modelList.appendChild(item);
        });
    } catch (error) {
        console.error('渲染模型列表出错:', error);
        showNotification('显示模型列表时出错', 'error');
    }
}

// 更新模型选择下拉框
function updateModelSelect() {
    const select = document.getElementById('selectedModel');
    if (!select) {
        console.error('无法找到selectedModel元素');
        return;
    }
    
    select.innerHTML = '<option value="">选择模型</option>';
    
    Object.keys(models).forEach(name => {
        const option = document.createElement('option');
        option.value = name;
        option.textContent = name;
        select.appendChild(option);
    });
    
    // 添加模型选择变更事件处理
    select.addEventListener('change', updateCurrentApiKey);
}

// 当选择模型时，更新当前API密钥
function updateCurrentApiKey() {
    try {
        const selectedModel = document.getElementById('selectedModel');
        const apiKeyInput = document.getElementById('currentApiKey');
        
        if (!selectedModel || !apiKeyInput) {
            console.warn('无法更新API密钥：元素不存在');
            return;
        }
        
        const modelName = selectedModel.value;
        
        // 如果选择了有效的模型并且模型配置存在
        if (modelName && models[modelName]) {
            // 模型配置中应该有API密钥
            const apiKey = models[modelName].api_key || '';
            apiKeyInput.value = apiKey;
            console.log(`已更新API密钥: ${modelName}`);
        } else {
            // 清空API密钥
            apiKeyInput.value = '';
        }
    } catch (error) {
        console.error('更新API密钥时出错:', error);
    }
}

// 添加模型
async function addModel() {
    const name = document.getElementById('modelName').value.trim();
    const provider = document.getElementById('modelProvider').value;
    const apiUrl = document.getElementById('modelApiUrl').value.trim();
    const modelName = document.getElementById('modelModelName').value.trim();
    const apiKey = document.getElementById('modelApiKey').value.trim();
    
    if (!name || !provider || !apiKey) {
        showNotification('请填写必要字段：名称、提供商和API密钥', 'error');
        return;
    }
    
    try {
        const response = await fetch('/api/models', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: name,
                provider: provider,
                api_key: apiKey,
                api_url: apiUrl || null,
                model_name: modelName || null
            })
        });
        
        if (response.ok) {
            showNotification('模型配置已保存', 'success');
            document.getElementById('modelName').value = '';
            document.getElementById('modelProvider').value = '';
            document.getElementById('modelApiUrl').value = '';
            document.getElementById('modelModelName').value = '';
            document.getElementById('modelApiKey').value = '';
            document.getElementById('providerInfo').classList.add('hidden');
            loadModels();
        } else {
            const error = await response.json();
            showNotification('保存失败: ' + error.detail, 'error');
        }
    } catch (error) {
        showNotification('保存失败: ' + error.message, 'error');
    }
}

// 删除模型
async function deleteModel(name) {
    if (!confirm(`确定要删除模型 "${name}" 吗？`)) {
        return;
    }
    
    try {
        const response = await fetch(`/api/models/${name}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showNotification('模型配置已删除', 'success');
            loadModels();
        } else {
            const error = await response.json();
            showNotification('删除失败: ' + error.detail, 'error');
        }
    } catch (error) {
        showNotification('删除失败: ' + error.message, 'error');
    }
}

// 加载MCP服务器配置
async function loadMCPServers() {
    try {
        console.log("开始加载MCP服务器列表...");
        const response = await fetch('/api/mcp-servers');
        
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }
        
        mcpServers = await response.json();
        console.log("从服务器获取的MCP服务器列表:", mcpServers);
        
        // 检查是否为空对象
        if (Object.keys(mcpServers).length === 0) {
            console.log("警告: 获取到空的MCP服务器列表");
        }
        
        // 检查DOM元素存在
        const serverList = document.getElementById('mcpList');
        if (!serverList) {
            console.error('mcpList元素不存在');
            showNotification('无法渲染MCP服务器列表：界面元素不存在', 'error');
        } else {
            renderMCPServerList();
        }
        
        // 检查DOM元素存在
        const checkboxes = document.getElementById('mcpServerCheckboxes');
        if (!checkboxes) {
            console.error('mcpServerCheckboxes元素不存在');
        } else {
            updateMCPServerCheckboxes();
        }
    } catch (error) {
        console.error('加载MCP服务器配置失败:', error);
        showNotification('加载MCP服务器配置失败: ' + error.message, 'error');
    }
}

// 渲染MCP服务器列表
function renderMCPServerList() {
    try {
        const serverList = document.getElementById('mcpList');
        if (!serverList) {
            console.error('无法找到mcpList元素');
            return;
        }
        
        serverList.innerHTML = '';
        
        if (Object.keys(mcpServers).length === 0) {
            serverList.innerHTML = '<div class="text-center p-4 text-gray-500">暂无MCP服务器配置</div>';
            return;
        }
        
        Object.entries(mcpServers).forEach(([name, config]) => {
            const item = document.createElement('div');
            item.className = 'config-item mb-2';
            
            // 获取健康状态
            const health = mcpServerHealth[name];
            const healthDisplay = health ? getHealthStatusDisplay(health.status) : getHealthStatusDisplay('unknown');
            
            // 创建简洁的服务器列表项
            item.innerHTML = `
                <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition cursor-pointer" 
                     onclick="toggleServerDetails(this, '${name}')">
                    <div class="flex items-center">
                        <div class="mr-2 text-gray-800 font-medium">${name}</div>
                        <span class="health-status ${healthDisplay.class} ml-2">
                            <i class="${healthDisplay.icon}"></i>
                            ${healthDisplay.text}
                        </span>
                    </div>
                    <div class="flex items-center">
                        <button class="health-check-btn mr-1" onclick="event.stopPropagation(); checkSingleMCPHealth('${name}')"
                                title="检查健康状态">
                            <i class="fas fa-heartbeat"></i>
                        </button>
                        <button class="health-check-btn mr-1" onclick="event.stopPropagation(); debugMCPHealth('${name}')"
                                title="调试健康检查" style="background-color: #3b82f6; color: white;">
                            <i class="fas fa-bug"></i>
                        </button>
                        ${config.connection_type === 'sse' ? `
                        <button class="health-check-btn mr-1" onclick="event.stopPropagation(); testSSEFunctionality('${name}')"
                                title="测试SSE功能" style="background-color: #10b981; color: white;">
                            <i class="fas fa-flask"></i>
                        </button>
                        ` : ''}
                        <button class="btn-sm btn-danger" onclick="event.stopPropagation(); deleteMCPServer('${name}')">
                            <i class="fas fa-trash"></i>
                        </button>
                        <i class="fas fa-chevron-down ml-2 text-gray-400 server-toggle-icon"></i>
                    </div>
                </div>
                <div class="server-details hidden bg-gray-50 p-3 rounded-lg mt-1 border border-gray-200"></div>
            `;
            
            serverList.appendChild(item);
        });
    } catch (error) {
        console.error('渲染MCP服务器列表出错:', error);
        showNotification('显示MCP服务器列表时出错', 'error');
    }
}

// 切换显示服务器详情
function toggleServerDetails(element, serverName) {
    const detailsElement = element.nextElementSibling;
    const chevronIcon = element.querySelector('.server-toggle-icon');
    
    // 检查是否已加载详情
    if (detailsElement.classList.contains('hidden')) {
        // 显示详情
        detailsElement.classList.remove('hidden');
        chevronIcon.classList.remove('fa-chevron-down');
        chevronIcon.classList.add('fa-chevron-up');
        
        // 如果未加载过详情，则加载
        if (detailsElement.innerHTML.trim() === '') {
            loadServerDetails(detailsElement, serverName);
        }
    } else {
        // 隐藏详情
        detailsElement.classList.add('hidden');
        chevronIcon.classList.remove('fa-chevron-up');
        chevronIcon.classList.add('fa-chevron-down');
    }
}

// 加载服务器详情
function loadServerDetails(detailsElement, serverName) {
    const config = mcpServers[serverName];
    if (!config) return;
    
    let configDetails = '';
    const connectionType = config.connection_type || 'stdio';
    
    if (connectionType === 'stdio') {
        const envInfo = config.env ? `环境变量: ${Object.keys(config.env).length}个` : '';
        const cwdInfo = config.cwd ? `工作目录: ${config.cwd}` : '';
        configDetails = `
            <div class="details">类型: 标准输入/输出 (stdio)</div>
            <div class="details">命令: ${config.command || ''} ${(config.args || []).join(' ')}</div>
            ${cwdInfo ? `<div class="details">${cwdInfo}</div>` : ''}
            ${envInfo ? `<div class="details">${envInfo}</div>` : ''}
        `;
    } else if (connectionType === 'sse') {
        const headersInfo = config.headers ? `请求头: ${Object.keys(config.headers).length}个` : '';
        configDetails = `
            <div class="details">类型: 服务器发送事件 (SSE)</div>
            <div class="details">URL: ${config.url || ''}</div>
            ${headersInfo ? `<div class="details">${headersInfo}</div>` : ''}
        `;
    } else if (connectionType === 'streamableHttp') {
        const headersInfo = config.headers ? `请求头: ${Object.keys(config.headers).length}个` : '';
        configDetails = `
            <div class="details">类型: 可流式传输HTTP</div>
            <div class="details">URL: ${config.url || ''}</div>
            ${headersInfo ? `<div class="details">${headersInfo}</div>` : ''}
        `;
    }
    
    // 高级配置信息
    const advancedConfig = `
        <div class="details">超时: ${config.timeout || 30}秒 | 重试: ${config.retry_count || 3}次</div>
    `;
    
    // 状态详情
    let statusDetails = '';
    const health = mcpServerHealth[serverName];
    if (health) {
        statusDetails = `
            <div class="server-status-details mt-2 p-2 bg-white rounded border border-gray-200">
                <div class="status-item">
                    <span class="label">检查时间:</span>
                    <span class="value">${formatTime(health.last_checked)}</span>
                </div>
                <div class="status-item">
                    <span class="label">消息:</span>
                    <span class="value">${health.message}</span>
                </div>
                ${health.response_time ? `
                <div class="status-item">
                    <span class="label">响应时间:</span>
                    <span class="value">${health.response_time}ms</span>
                </div>
                ` : ''}
            </div>
        `;
    }
    
    // 组合详情内容
    detailsElement.innerHTML = `
        <div class="space-y-2">
            ${config.description ? `<div class="details">${config.description}</div>` : ''}
            ${configDetails}
            ${advancedConfig}
            ${statusDetails}
        </div>
    `;
}

// 更新MCP服务器复选框
function updateMCPServerCheckboxes() {
    try {
        const container = document.getElementById('mcpServerCheckboxes');
        if (!container) {
            console.error('无法找到mcpServerCheckboxes元素');
            return;
        }
        
        container.innerHTML = '';
        
        Object.keys(mcpServers).forEach(name => {
            const health = mcpServerHealth[name];
            const healthDisplay = health ? getHealthStatusDisplay(health.status) : getHealthStatusDisplay('unknown');
            
            const checkbox = document.createElement('label');
            checkbox.className = 'mcp-checkbox';
            
            // 如果服务器有问题，添加视觉提示
            if (health && health.status === 'error') {
                checkbox.style.borderColor = '#f87171';
                checkbox.style.backgroundColor = '#fef2f2';
            }
            
            checkbox.innerHTML = `
                <input type="checkbox" value="${name}" onchange="toggleMCPServer(this)">
                <span>${name}</span>
                <span class="health-status ${healthDisplay.class}" style="margin-left: 4px;">
                    <i class="${healthDisplay.icon}"></i>
                </span>
            `;
            container.appendChild(checkbox);
        });
    } catch (error) {
        console.error('更新MCP服务器复选框出错:', error);
    }
}

// 切换MCP服务器选择状态
function toggleMCPServer(checkbox) {
    const label = checkbox.parentElement;
    if (checkbox.checked) {
        label.classList.add('active');
    } else {
        label.classList.remove('active');
    }
}

// 验证JSON格式
function validateJSON(jsonString) {
    try {
        JSON.parse(jsonString);
        return true;
    } catch (e) {
        return false;
    }
}

// 添加MCP服务器
async function addMCPServer() {
    try {
        const nameInput = document.getElementById('mcpName');
        const descInput = document.getElementById('mcpDescription');
        const typeSelect = document.getElementById('mcpConnectionType');
        
        console.log('nameInput:', nameInput);
        console.log('descInput:', descInput);
        console.log('typeSelect:', typeSelect);
        
        if (!nameInput || !typeSelect) {
            showNotification('表单元素不存在', 'error');
            return;
        }
        
        const name = nameInput.value.trim();
        const description = descInput ? descInput.value.trim() : '';
        const connectionType = typeSelect.value;
        
        if (!name) {
            showNotification('请输入服务器名称', 'error');
            return;
        }
        
        let requestData = {
            name: name,
            description: description,
            connection_type: connectionType,
            timeout: 30,  // 默认值
            retry_count: 3  // 默认值
        };
        
        if (connectionType === 'stdio') {
            const commandInput = document.getElementById('mcpCommand');
            const argsInput = document.getElementById('mcpArgs');
            
            console.log('commandInput:', commandInput);
            console.log('argsInput:', argsInput);
            
            if (!commandInput) {
                showNotification('命令输入框不存在', 'error');
                return;
            }
            
            const command = commandInput.value.trim();
            if (!command) {
                showNotification('请输入启动命令', 'error');
                return;
            }
            
            const args = argsInput && argsInput.value.trim() 
                ? argsInput.value.trim().split(',').map(arg => arg.trim()) 
                : [];
            
            // 收集环境变量（可选）
            const env = {};
            const envContainer = document.getElementById('envVarsContainer');
            console.log('envContainer:', envContainer);
            if (envContainer) {
                envContainer.querySelectorAll('.env-row').forEach(row => {
                    const keyInput = row.querySelector('input:first-child');
                    const valueInput = row.querySelector('input:nth-child(2)');
                    if (keyInput && valueInput) {
                        const key = keyInput.value.trim();
                        const value = valueInput.value.trim();
                        if (key && value) {
                            env[key] = value;
                        }
                    }
                });
            }
            
            requestData.command = command;
            requestData.args = args;
            if (Object.keys(env).length > 0) {
                requestData.env = env;
            }
            
        } else if (connectionType === 'sse' || connectionType === 'streamableHttp') {
            const urlInput = document.getElementById('mcpUrl');
            
            console.log('urlInput:', urlInput);
            
            if (!urlInput) {
                showNotification('URL输入框不存在', 'error');
                return;
            }
            
            const url = urlInput.value.trim();
            if (!url) {
                showNotification('请输入URL地址', 'error');
                return;
            }
            
            // 收集请求头（可选）
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'text/event-stream'
            };
            const headersContainer = document.getElementById('headersContainer');
            console.log('headersContainer:', headersContainer);
            if (headersContainer) {
                headersContainer.querySelectorAll('.header-row').forEach(row => {
                    const keyInput = row.querySelector('input:first-child');
                    const valueInput = row.querySelector('input:nth-child(2)');
                    if (keyInput && valueInput) {
                        const key = keyInput.value.trim();
                        const value = valueInput.value.trim();
                        if (key && value) {
                            headers[key] = value;
                        }
                    }
                });
            }
            
            requestData.url = url;
            requestData.headers = headers;
        }
        
        const response = await fetch('/api/mcp-servers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        if (response.ok) {
            const result = await response.json();
            showNotification('MCP服务器配置已保存', 'success');
            
            // 更新健康状态
            if (result.health_status) {
                mcpServerHealth[name] = result.health_status;
            }
            
            // 清空表单
            clearMCPForm();
            
            // 隐藏表单
            toggleMCPForm();
            
            // 重新加载MCP服务器列表
            await loadMCPServers();
            
            // 确保MCP配置区域可见
            showMCPConfig();
            
            console.log("已加载的MCP服务器:", mcpServers);
        } else {
            const error = await response.json();
            showNotification('保存失败: ' + error.detail, 'error');
        }
    } catch (error) {
        console.error('添加MCP服务器时出错:', error);
        showNotification('保存失败: ' + (error.message || '未知错误'), 'error');
    }
}

// 清空MCP配置表单
function clearMCPForm() {
    document.getElementById('mcpName').value = '';
    document.getElementById('mcpDescription').value = '';
    document.getElementById('mcpConnectionType').value = 'stdio';
    document.getElementById('mcpCommand').value = '';
    document.getElementById('mcpArgs').value = '';
    document.getElementById('mcpUrl').value = '';
    
    // 清空环境变量
    document.getElementById('envVarsContainer').innerHTML = '';
    
    // 重置请求头为默认值
    const headersContainer = document.getElementById('headersContainer');
    headersContainer.innerHTML = `
        <div class="header-row flex space-x-2">
            <input type="text" placeholder="Content-Type" value="Content-Type" 
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <input type="text" placeholder="application/json" value="application/json" 
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <button type="button" onclick="removeHeaderRow(this)" 
                    class="text-red-600 hover:text-red-800 px-2">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="header-row flex space-x-2">
            <input type="text" placeholder="Authorization" value="Authorization" 
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <input type="text" placeholder="Bearer token" value="Bearer " 
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <button type="button" onclick="removeHeaderRow(this)" 
                    class="text-red-600 hover:text-red-800 px-2">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    // 重置连接类型字段显示
    toggleMCPConnectionFields();
}

// 删除MCP服务器
async function deleteMCPServer(name) {
    if (!confirm(`确定要删除MCP服务器 "${name}" 吗？`)) {
        return;
    }
    
    try {
        const response = await fetch(`/api/mcp-servers/${name}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showNotification('MCP服务器配置已删除', 'success');
            delete mcpServerHealth[name]; // 删除健康状态
            loadMCPServers();
        } else {
            const error = await response.json();
            showNotification('删除失败: ' + error.detail, 'error');
        }
    } catch (error) {
        showNotification('删除失败: ' + error.message, 'error');
    }
}

// 获取选中的MCP服务器
function getSelectedMCPServers() {
    const checkboxes = document.querySelectorAll('#mcpServerCheckboxes input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// 更新聊天功能以支持流式输出
function sendMessage() {
    const message = document.getElementById('messageInput').value.trim();
    const selectedModel = document.getElementById('selectedModel').value;
    const apiKey = document.getElementById('currentApiKey').value.trim();
    
    // 获取选中的MCP服务器
    const mcpServers = [];
    document.querySelectorAll('#mcpServerCheckboxes input[type="checkbox"]:checked').forEach(checkbox => {
        mcpServers.push(checkbox.value);
    });
    
    if (!message) {
        showToast('请输入消息', 'warning');
        return;
    }
    
    if (!selectedModel) {
        showToast('请选择模型', 'warning');
        return;
    }
    
    // 添加用户消息到聊天界面
    appendChatMessage('user', message);
    
    // 清空输入框并禁用发送按钮
    document.getElementById('messageInput').value = '';
    document.getElementById('sendButton').disabled = true;
    
    // 显示AI回复的占位符
    const assistantMessageId = 'assistant-message-' + Date.now();
    const assistantContentId = 'assistant-content-' + Date.now();
    
    // 创建助手消息元素
    const assistantDiv = document.createElement('div');
    assistantDiv.id = assistantMessageId;
    assistantDiv.className = `message assistant-message mb-4 p-3 rounded-lg bg-gray-50`;
    
    // 添加角色标签
    const roleLabel = document.createElement('div');
    roleLabel.className = 'font-medium text-xs text-gray-500 mb-1';
    roleLabel.textContent = 'AI';
    
    // 创建消息内容
    const contentDiv = document.createElement('div');
    contentDiv.id = assistantContentId;
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = '<div class="typing-indicator"><span></span><span></span><span></span></div>';
    
    // 构建消息
    assistantDiv.appendChild(roleLabel);
    assistantDiv.appendChild(contentDiv);
    document.getElementById('chatContainer').appendChild(assistantDiv);
    scrollChatToBottom();
    
    // 检查是否启用流式输出
    const streamToggle = document.getElementById('streamToggle');
    const useStreamMode = streamToggle ? streamToggle.checked : useStreaming;
    
    // 请求参数
    const params = {
        message: message,
        model: selectedModel,
        api_key: apiKey,
        mcp_servers: mcpServers,
        stream: useStreamMode
    };
    
    // 获取消息内容元素
    const assistantContent = document.getElementById(assistantContentId);
    
    if (useStreamMode) {
        // 流式输出 - 使用我们自己的函数
        // 因为handleStreamResponse是在chat.js中定义的，我们这里使用fetchChatStreamResponse
        fetchChatStreamResponse(params, assistantContentId);
    } else {
        // 标准响应
        fetchChatResponse(params, assistantContentId);
    }
}

// 标准响应请求
async function fetchChatResponse(params, contentId) {
    try {
        const assistantContent = document.getElementById(contentId);
        assistantContent.innerHTML = '<div class="typing-indicator"><span></span><span></span><span></span></div>';
        
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '请求失败');
        }
        
        const data = await response.json();
        
        // 更新AI回复内容
        assistantContent.innerHTML = formatMessageContent(data.response);
        
        // 恢复发送按钮状态
        document.getElementById('sendButton').disabled = false;
        
        // 滚动到底部
        scrollChatToBottom();
        
    } catch (error) {
        console.error('Chat error:', error);
        const assistantContent = document.getElementById(contentId);
        assistantContent.innerHTML = `<div class="error text-red-500">错误: ${error.message}</div>`;
        document.getElementById('sendButton').disabled = false;
    }
}

// 流式响应请求
async function fetchChatStreamResponse(params, assistantMessageId) {
    try {
        // 准备查询参数
        const queryParams = new URLSearchParams({
            message: params.message,
            model: params.model,
            stream: 'true'
        });
        
        if (params.api_key) {
            queryParams.append('api_key', params.api_key);
        }
        
        if (params.mcp_servers && params.mcp_servers.length > 0) {
            queryParams.append('mcp_servers', params.mcp_servers.join(','));
        }
        
        // 完整URL
        const streamUrl = `/api/chat?${queryParams.toString()}`;
        console.log("建立SSE连接:", streamUrl);
        
        // 创建事件源
        const eventSource = new EventSource(streamUrl);
        
        const assistantMessage = document.getElementById(assistantMessageId);
        assistantMessage.innerHTML = ''; // 清除打字指示器
        
        let fullResponse = '';
        let eventCount = 0;
        
        // 处理连接建立
        eventSource.onopen = function() {
            console.log("SSE连接已建立");
        };
        
        // 处理服务器发送的事件
        eventSource.onmessage = function(event) {
            try {
                eventCount++;
                console.log(`收到SSE事件 #${eventCount}:`, event.data);
                
                const data = JSON.parse(event.data);
                
                if (data.done) {
                    // 流结束
                    console.log("流结束，关闭SSE连接 (收到完成事件)");
                    eventSource.close();
                    document.getElementById('sendButton').disabled = false;
                } else {
                    // 添加新内容
                    if (data.content) {
                        fullResponse += data.content;
                        assistantMessage.innerHTML = formatMessageContent(fullResponse);
                        scrollChatToBottom();
                    }
                }
            } catch (e) {
                console.error('解析事件数据失败:', e, event.data);
            }
        };
        
        // 处理错误
        eventSource.onerror = function(error) {
            console.error('SSE错误:', error);
            
            // 在某些情况下，这可能是正常的完成信号
            if (fullResponse && eventCount > 0) {
                console.log("SSE连接关闭 (可能是正常完成)");
            } else {
                console.error("SSE连接出错，未收到任何内容");
                if (!fullResponse) {
                    assistantMessage.innerHTML = '<div class="text-red-500">流式响应失败</div>';
                }
            }
            
            eventSource.close();
            document.getElementById('sendButton').disabled = false;
        };
        
    } catch (error) {
        console.error('Stream error:', error);
        const assistantMessage = document.getElementById(assistantMessageId);
        assistantMessage.innerHTML = `<div class="text-red-500">错误: ${error.message}</div>`;
        document.getElementById('sendButton').disabled = false;
    }
}

// 格式化消息内容(支持Markdown和代码高亮)
function formatMessageContent(content) {
    if (!content) return '';
    
    // 基本格式化
    let formatted = content
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/\n/g, '<br>')
        .replace(/\r/g, '');
    
    // 代码块格式化
    formatted = formatted.replace(/```(\w+)?\s*([\s\S]*?)\s*```/g, function(match, lang, code) {
        return `<pre class="bg-gray-100 p-3 rounded-md overflow-x-auto"><code class="${lang || ''}">${code}</code></pre>`;
    });
    
    // 行内代码格式化
    formatted = formatted.replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>');
    
    return formatted;
}

// 全局变量，控制是否使用流式输出
let useStreaming = true;

// 监听DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 绑定流式输出开关
    const streamToggle = document.getElementById('streamToggle');
    if (streamToggle) {
        streamToggle.addEventListener('change', function() {
            useStreaming = this.checked;
            console.log('流式输出状态:', useStreaming ? '开启' : '关闭');
        });
    }
});

// 加载聊天历史
async function loadChatHistory() {
    try {
        const response = await fetch('/api/chat-history');
        chatHistory = await response.json();
        
        const chatContainer = document.getElementById('chatContainer');
        if (!chatContainer) {
            console.error('聊天容器元素不存在');
            return;
        }
        
        chatContainer.innerHTML = '';
        
        chatHistory.forEach(chat => {
            appendChatMessage('user', chat.user);
            appendChatMessage('assistant', chat.assistant);
        });
    } catch (error) {
        console.error('加载聊天历史失败:', error);
    }
}

// 清空聊天历史
async function clearHistory() {
    if (!confirm('确定要清空聊天历史并开始新对话吗？')) {
        return;
    }
    
    try {
        // 清空全局聊天历史
        const response = await fetch('/api/chat-history', {
            method: 'DELETE'
        });
        
        // 同时清空当前会话上下文
        const sessionResponse = await fetch('/api/sessions/default', {
            method: 'DELETE'
        });
        
        if (response.ok && sessionResponse.ok) {
            document.getElementById('chatContainer').innerHTML = '';
            showNotification('聊天历史已清空，开始新对话', 'success');
        } else {
            const error = await response.json();
            showNotification('清空失败: ' + error.detail, 'error');
        }
    } catch (error) {
        showNotification('清空失败: ' + error.message, 'error');
    }
}

// 调试健康检查 - 提供详细错误信息
async function debugMCPHealth(serverName = null) {
    const indicator = document.getElementById('healthCheckIndicator');
    
    // 添加null检查
    if (indicator) {
        indicator.classList.remove('hidden');
    }
    
    try {
        const serverNames = serverName ? [serverName] : [];
        const response = await fetch('/api/mcp-servers/debug-health-check', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ server_names: serverNames })
        });
        
        if (response.ok) {
            const results = await response.json();
            
            // 显示调试信息
            for (const [name, result] of Object.entries(results)) {
                console.group(`🔍 调试健康检查: ${name}`);
                console.log('状态:', result.status);
                console.log('消息:', result.message);
                
                if (result.debug && result.debug.checks) {
                    console.log('详细检查步骤:');
                    result.debug.checks.forEach(check => console.log('  ', check));
                }
                
                if (result.debug && result.debug.config) {
                    console.log('服务器配置:', result.debug.config);
                }
                
                console.groupEnd();
                
                // 创建详细的调试弹窗
                showDebugModal(name, result);
            }
            
            // 更新健康状态
            Object.assign(mcpServerHealth, results);
            renderMCPServerList();
            updateMCPServerCheckboxes();
            
        } else {
            showNotification('调试健康检查失败', 'error');
        }
    } catch (error) {
        showNotification('调试健康检查失败: ' + error.message, 'error');
    } finally {
        // 添加null检查
        if (indicator) {
            indicator.classList.add('hidden');
        }
    }
}

// 显示调试模态框
function showDebugModal(serverName, result) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-2xl max-h-96 overflow-y-auto m-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    🔍 调试信息: ${serverName}
                </h3>
                <button onclick="this.closest('.fixed').remove()" 
                        class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <h4 class="font-medium text-gray-700 mb-2">状态信息</h4>
                    <div class="bg-gray-50 rounded p-3">
                        <div class="grid grid-cols-2 gap-2 text-sm">
                            <div><strong>状态:</strong> ${getStatusBadge(result.status)}</div>
                            <div><strong>响应时间:</strong> ${result.response_time || 'N/A'}ms</div>
                            <div class="col-span-2"><strong>消息:</strong> ${result.message}</div>
                        </div>
                    </div>
                </div>
                
                ${result.debug && result.debug.checks ? `
                <div>
                    <h4 class="font-medium text-gray-700 mb-2">检查步骤</h4>
                    <div class="bg-gray-50 rounded p-3">
                        <div class="space-y-1 text-sm font-mono">
                            ${result.debug.checks.map(check => 
                                `<div class="text-xs">${check}</div>`
                            ).join('')}
                        </div>
                    </div>
                </div>
                ` : ''}
                
                ${result.debug && result.debug.config ? `
                <div>
                    <h4 class="font-medium text-gray-700 mb-2">服务器配置</h4>
                    <div class="bg-gray-50 rounded p-3">
                        <pre class="text-xs text-gray-600 overflow-x-auto">${JSON.stringify(result.debug.config, null, 2)}</pre>
                    </div>
                </div>
                ` : ''}
            </div>
            
            <div class="mt-6 flex justify-end space-x-2">
                <button onclick="this.closest('.fixed').remove()" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">
                    关闭
                </button>
                <button onclick="debugMCPHealth('${serverName}')" 
                        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition">
                    重新检查
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// 获取状态徽章HTML
function getStatusBadge(status) {
    const statusConfig = {
        'healthy': { class: 'bg-green-100 text-green-800', icon: 'fas fa-check-circle', text: '健康' },
        'warning': { class: 'bg-yellow-100 text-yellow-800', icon: 'fas fa-exclamation-triangle', text: '警告' },
        'error': { class: 'bg-red-100 text-red-800', icon: 'fas fa-times-circle', text: '错误' },
        'unknown': { class: 'bg-gray-100 text-gray-800', icon: 'fas fa-question-circle', text: '未知' }
    };
    
    const config = statusConfig[status] || statusConfig['unknown'];
    return `<span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded ${config.class}">
        <i class="${config.icon} mr-1"></i>
        ${config.text}
    </span>`;
}

// 测试SSE服务器的完整功能
async function testSSEFunctionality(serverName) {
    if (!serverName || !mcpServers[serverName]) {
        showNotification('服务器不存在', 'error');
        return;
    }
    
    const config = mcpServers[serverName];
    
    if (config.connection_type !== 'sse') {
        showNotification('此服务器不是SSE类型', 'warning');
        return;
    }
    
    try {
        const response = await fetch('/api/mcp-servers/test-sse', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                url: config.url,
                headers: config.headers || {},
                tool: 'ping',
                params: { test: 'functionality_check' }
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            showSSETestResults(serverName, result);
        } else {
            const error = await response.json();
            showNotification(`SSE测试失败: ${error.detail}`, 'error');
        }
    } catch (error) {
        showNotification(`SSE测试失败: ${error.message}`, 'error');
    }
}

// 显示SSE测试结果
function showSSETestResults(serverName, result) {
    // 创建结果显示模态框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    
    const summary = result.summary || {};
    const recommendations = result.recommendations || [];
    
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-4xl max-h-96 overflow-y-auto m-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    🧪 SSE功能测试结果: ${serverName}
                </h3>
                <button onclick="this.closest('.fixed').remove()" 
                        class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- 测试总结 -->
            <div class="mb-4">
                <h4 class="font-medium text-gray-700 mb-2">📊 测试总结</h4>
                <div class="bg-gray-50 rounded p-3">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="flex items-center">
                            <span class="font-medium">连接成功:</span>
                            <span class="ml-2">${summary.connection_successful ? '✅' : '❌'}</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium">请求发送:</span>
                            <span class="ml-2">${summary.request_sent ? '✅' : '❌'}</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium">收到事件:</span>
                            <span class="ml-2">${summary.events_count || 0} 个</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium">错误数量:</span>
                            <span class="ml-2">${summary.errors_count || 0} 个</span>
                        </div>
                        <div class="flex items-center col-span-2">
                            <span class="font-medium">健康状态:</span>
                            <span class="ml-2">${getHealthStatusBadge(summary.health_status)}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 建议 -->
            <div class="mb-4">
                <h4 class="font-medium text-gray-700 mb-2">💡 建议</h4>
                <div class="bg-blue-50 rounded p-3">
                    <div class="space-y-1 text-sm">
                        ${recommendations.map(rec => `<div>${rec}</div>`).join('')}
                    </div>
                </div>
            </div>
            
            <!-- 详细结果 -->
            <div class="mb-4">
                <h4 class="font-medium text-gray-700 mb-2">🔍 详细结果</h4>
                <div class="bg-gray-50 rounded p-3">
                    <pre class="text-xs text-gray-600 overflow-x-auto max-h-32">${JSON.stringify(result.results, null, 2)}</pre>
                </div>
            </div>
            
            <div class="flex justify-end space-x-2">
                <button onclick="testSSEFunctionality('${serverName}')" 
                        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition">
                    🔄 重新测试
                </button>
                <button onclick="this.closest('.fixed').remove()" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">
                    关闭
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// 获取状态徽章HTML
function getHealthStatusBadge(status) {
    const statusConfig = {
        'healthy': { class: 'bg-green-100 text-green-800', icon: 'fas fa-check-circle', text: '健康' },
        'warning': { class: 'bg-yellow-100 text-yellow-800', icon: 'fas fa-exclamation-triangle', text: '警告' },
        'error': { class: 'bg-red-100 text-red-800', icon: 'fas fa-times-circle', text: '错误' },
        'unknown': { class: 'bg-gray-100 text-gray-800', icon: 'fas fa-question-circle', text: '未知' }
    };
    
    const config = statusConfig[status] || statusConfig['unknown'];
    return `<span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded ${config.class}">
        <i class="${config.icon} mr-1"></i>
        ${config.text}
    </span>`;
}

// 向聊天区域添加消息
function appendChatMessage(role, content, messageId = null) {
    const chatContainer = document.getElementById('chatContainer');
    const messageDiv = document.createElement('div');
    
    // 改进消息样式
    messageDiv.className = `message ${role}-message mb-4 p-3 rounded-lg ${role === 'user' ? 'bg-blue-50' : 'bg-gray-50'}`;
    
    // 设置唯一ID
    if (messageId) {
        messageDiv.id = messageId;
    } else {
        messageDiv.id = `${role}-message-${Date.now()}`;
    }
    
    // 添加角色标签
    const roleLabel = document.createElement('div');
    roleLabel.className = 'font-medium text-xs text-gray-500 mb-1';
    roleLabel.textContent = role === 'user' ? '您' : 'AI';
    
    // 创建消息内容
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    if (role === 'user') {
        contentDiv.textContent = content;
    } else {
        contentDiv.innerHTML = content;
    }
    
    // 添加到消息div
    messageDiv.appendChild(roleLabel);
    messageDiv.appendChild(contentDiv);
    chatContainer.appendChild(messageDiv);
    
    // 滚动到底部
    scrollChatToBottom();
    
    return messageDiv.id;
}

// 滚动聊天区域到底部
function scrollChatToBottom() {
    const chatContainer = document.getElementById('chatContainer');
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

// 显示提示信息
function showToast(message, type = 'info') {
    // 如果页面上还没有toast容器，创建一个
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-2';
        document.body.appendChild(toastContainer);
    }
    
    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = `p-3 rounded shadow-lg transition-opacity duration-300 opacity-0`;
    
    // 根据类型设置颜色
    switch (type) {
        case 'success':
            toast.classList.add('bg-green-500', 'text-white');
            break;
        case 'warning':
            toast.classList.add('bg-yellow-500', 'text-white');
            break;
        case 'error':
            toast.classList.add('bg-red-500', 'text-white');
            break;
        default:
            toast.classList.add('bg-blue-500', 'text-white');
    }
    
    toast.innerHTML = message;
    toastContainer.appendChild(toast);
    
    // 显示toast
    setTimeout(() => {
        toast.classList.add('opacity-100');
    }, 10);
    
    // 3秒后淡出
    setTimeout(() => {
        toast.classList.remove('opacity-100');
        toast.classList.add('opacity-0');
        
        // 动画结束后移除元素
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

// 创建新对话，保留历史但清除当前会话上下文
async function newConversation() {
    try {
        // 清空当前会话上下文
        const sessionId = 'default'; // 使用默认会话ID
        const response = await fetch(`/api/sessions/${sessionId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            // 清空聊天区域，但不删除历史记录
            document.getElementById('chatContainer').innerHTML = '';
            showNotification('开始新对话', 'success');
        } else {
            const error = await response.json();
            showNotification('创建新对话失败: ' + error.detail, 'error');
        }
    } catch (error) {
        showNotification('创建新对话失败: ' + error.message, 'error');
    }
}

// 获取MCP服务器提供的工具列表
async function getMCPTools(serverName) {
    try {
        const response = await fetch(`/api/mcp-tools/${serverName}`);
        
        if (response.ok) {
            return await response.json();
        } else {
            console.error(`获取服务器 ${serverName} 工具列表失败: HTTP ${response.status}`);
            return { 
                status: "error", 
                message: `HTTP错误: ${response.status}`,
                tools: [] 
            };
        }
    } catch (error) {
        console.error('获取MCP工具列表失败:', error);
        return { 
            status: "error", 
            message: error.message,
            tools: [] 
        };
    }
}

// 调用MCP工具
async function callMCPTool(serverName, toolName, params) {
    try {
        const response = await fetch(`/api/mcp-tools/${serverName}/call`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                tool: toolName,
                params: params
            })
        });
        
        if (response.ok) {
            return await response.json();
        } else {
            console.error(`调用服务器 ${serverName} 工具 ${toolName} 失败: HTTP ${response.status}`);
            const errorData = await response.json();
            return { 
                status: "error", 
                message: errorData.detail || `HTTP错误: ${response.status}`
            };
        }
    } catch (error) {
        console.error('调用MCP工具失败:', error);
        return { 
            status: "error", 
            message: error.message 
        };
    }
}

// 显示MCP工具列表
function showMCPTools() {
    const selectedServers = getSelectedMCPServers();
    
    if (!selectedServers || selectedServers.length === 0) {
        showNotification('请先选择MCP服务器', 'warning');
        return;
    }
    
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-4xl max-h-96 overflow-y-auto m-4 w-full">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    🧰 可用的MCP工具
                </h3>
                <button onclick="this.closest('.fixed').remove()" 
                        class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div id="tool-list-container" class="space-y-4">
                <div class="flex justify-center">
                    <div class="loading-small"></div>
                    <p class="ml-2">正在加载工具列表...</p>
                </div>
            </div>
            
            <div class="mt-6 flex justify-end">
                <button onclick="this.closest('.fixed').remove()" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">
                    关闭
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 加载工具列表
    loadMCPToolsList(selectedServers);
}

// 加载MCP工具列表
async function loadMCPToolsList(servers) {
    const container = document.getElementById('tool-list-container');
    
    try {
        container.innerHTML = '<div class="flex justify-center"><div class="loading-small"></div><p class="ml-2">正在加载工具列表...</p></div>';
        
        const toolsByServer = [];
        
        // 获取每个服务器的工具
        for (const server of servers) {
            const result = await getMCPTools(server);
            
            if (result.status === "success" && result.tools && result.tools.length > 0) {
                toolsByServer.push({
                    server: server,
                    tools: result.tools
                });
            } else {
                console.warn(`服务器 ${server} 没有返回有效的工具: ${result.message}`);
            }
        }
        
        // 显示工具列表
        if (toolsByServer.length === 0) {
            container.innerHTML = '<div class="text-center text-gray-500">没有找到可用的MCP工具</div>';
            return;
        }
        
        container.innerHTML = '';
        
        for (const serverInfo of toolsByServer) {
            const serverName = serverInfo.server;
            const tools = serverInfo.tools;
            
            const serverSection = document.createElement('div');
            serverSection.className = 'mb-6';
            
            serverSection.innerHTML = `
                <h4 class="text-md font-medium text-gray-800 mb-2">
                    📁 服务器: ${serverName}
                </h4>
                <div class="bg-gray-50 rounded-lg p-4 tools-grid">
                </div>
            `;
            
            const toolsGrid = serverSection.querySelector('.tools-grid');
            
            for (const tool of tools) {
                const toolName = tool.name || "未命名工具";
                const toolDescription = tool.description || "无描述";
                
                const toolCard = document.createElement('div');
                toolCard.className = 'tool-card bg-white p-3 rounded border border-gray-200 hover:border-blue-300 transition';
                toolCard.innerHTML = `
                    <div class="font-medium text-blue-600">${toolName}</div>
                    <div class="text-sm text-gray-600 mt-1">${toolDescription}</div>
                    <div class="mt-3 text-xs">
                        <button class="bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200 transition"
                                onclick="showToolDetails('${serverName}', ${JSON.stringify(tool).replace(/"/g, '&quot;')})">
                            查看详情
                        </button>
                        <button class="bg-green-100 text-green-700 px-2 py-1 rounded hover:bg-green-200 transition"
                                onclick="showToolCallForm('${serverName}', ${JSON.stringify(tool).replace(/"/g, '&quot;')})">
                            调用工具
                        </button>
                    </div>
                `;
                
                toolsGrid.appendChild(toolCard);
            }
            
            container.appendChild(serverSection);
        }
        
        // 添加CSS样式
        const style = document.createElement('style');
        style.textContent = `
            .tools-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 12px;
            }
            .tool-card {
                display: flex;
                flex-direction: column;
            }
        `;
        container.appendChild(style);
        
    } catch (error) {
        container.innerHTML = `<div class="text-center text-red-500">加载工具列表失败: ${error.message}</div>`;
    }
}

// 显示工具详情
function showToolDetails(serverName, tool) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    
    const paramsList = tool.params ? Object.entries(tool.params).map(([key, value]) => {
        return `<div class="mb-2">
            <span class="font-medium">${key}</span>: 
            <span class="text-gray-600">${JSON.stringify(value)}</span>
        </div>`;
    }).join('') : '<div class="text-gray-500">无参数</div>';
    
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-2xl max-h-96 overflow-y-auto m-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    🔍 工具详情: ${tool.name || "未命名工具"}
                </h3>
                <button onclick="this.closest('.fixed').remove()" 
                        class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <h4 class="font-medium text-gray-700 mb-2">服务器</h4>
                    <div class="bg-gray-50 rounded p-3">
                        ${serverName}
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-700 mb-2">工具名称</h4>
                    <div class="bg-gray-50 rounded p-3">
                        ${tool.name || "未命名工具"}
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-700 mb-2">描述</h4>
                    <div class="bg-gray-50 rounded p-3">
                        ${tool.description || "无描述"}
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-700 mb-2">参数</h4>
                    <div class="bg-gray-50 rounded p-3">
                        ${paramsList}
                    </div>
                </div>
            </div>
            
            <div class="mt-6 flex justify-end space-x-2">
                <button onclick="this.closest('.fixed').remove()" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">
                    关闭
                </button>
                <button onclick="showToolCallForm('${serverName}', ${JSON.stringify(tool).replace(/"/g, '&quot;')}); this.closest('.fixed').remove()" 
                        class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition">
                    调用工具
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// 显示工具调用表单
function showToolCallForm(serverName, tool) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    
    // 生成参数输入字段
    let paramsInputs = '';
    if (tool.params) {
        paramsInputs = Object.entries(tool.params).map(([key, schema]) => {
            // 根据参数类型生成不同的输入控件
            const isRequired = schema.required || false;
            const description = schema.description || '';
            const type = schema.type || 'string';
            
            let inputHtml = '';
            if (type === 'boolean') {
                inputHtml = `
                    <div class="flex items-center">
                        <input type="checkbox" id="param-${key}" name="${key}" class="mr-2">
                        <label for="param-${key}" class="text-sm text-gray-600">
                            ${description}
                        </label>
                    </div>
                `;
            } else if (type === 'number' || type === 'integer') {
                inputHtml = `
                    <input type="number" id="param-${key}" name="${key}" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="${description}">
                `;
            } else if (type === 'array') {
                inputHtml = `
                    <textarea id="param-${key}" name="${key}" rows="3"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入数组值，每行一个项目"></textarea>
                    <div class="text-xs text-gray-500 mt-1">每行输入一个数组元素</div>
                `;
            } else if (type === 'object') {
                inputHtml = `
                    <textarea id="param-${key}" name="${key}" rows="5"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入JSON对象"></textarea>
                    <div class="text-xs text-gray-500 mt-1">请输入有效的JSON格式</div>
                `;
            } else {
                // 默认为字符串类型
                inputHtml = `
                    <input type="text" id="param-${key}" name="${key}" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="${description}">
                `;
            }
            
            return `
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1" for="param-${key}">
                        ${key} ${isRequired ? '<span class="text-red-500">*</span>' : ''}
                    </label>
                    ${inputHtml}
                </div>
            `;
        }).join('');
    }
    
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-2xl max-h-96 overflow-y-auto m-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    🛠️ 调用工具: ${tool.name || "未命名工具"}
                </h3>
                <button onclick="this.closest('.fixed').remove()" 
                        class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="tool-call-form" class="space-y-4">
                <input type="hidden" id="server-name" value="${serverName}">
                <input type="hidden" id="tool-name" value="${tool.name}">
                
                <div>
                    <div class="font-medium text-gray-700 mb-1">服务器</div>
                    <div class="bg-gray-50 rounded p-2 text-gray-600">
                        ${serverName}
                    </div>
                </div>
                
                <div>
                    <div class="font-medium text-gray-700 mb-1">工具名称</div>
                    <div class="bg-gray-50 rounded p-2 text-gray-600">
                        ${tool.name || "未命名工具"}
                    </div>
                </div>
                
                <div>
                    <div class="font-medium text-gray-700 mb-1">描述</div>
                    <div class="bg-gray-50 rounded p-2 text-gray-600">
                        ${tool.description || "无描述"}
                    </div>
                </div>
                
                <div>
                    <div class="font-medium text-gray-700 mb-3">参数</div>
                    <div class="bg-gray-50 rounded p-4">
                        ${paramsInputs || '<div class="text-gray-500">无需参数</div>'}
                    </div>
                </div>
                
                <div id="tool-result" class="hidden">
                    <div class="font-medium text-gray-700 mb-1">结果</div>
                    <div class="bg-gray-50 rounded p-4 max-h-40 overflow-y-auto">
                        <pre id="result-content" class="text-sm whitespace-pre-wrap"></pre>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-2">
                    <button type="button" onclick="this.closest('.fixed').remove()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">
                        关闭
                    </button>
                    <button type="submit" id="call-button"
                            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition">
                        调用工具
                    </button>
                </div>
            </form>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 绑定表单提交事件
    document.getElementById('tool-call-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const serverName = document.getElementById('server-name').value;
        const toolName = document.getElementById('tool-name').value;
        
        // 收集参数
        const params = {};
        if (tool.params) {
            for (const [key, schema] of Object.entries(tool.params)) {
                const type = schema.type || 'string';
                const paramInput = document.getElementById(`param-${key}`);
                
                if (!paramInput) continue;
                
                if (type === 'boolean') {
                    params[key] = paramInput.checked;
                } else if (type === 'number' || type === 'integer') {
                    const value = paramInput.value.trim();
                    params[key] = value ? (type === 'integer' ? parseInt(value) : parseFloat(value)) : null;
                } else if (type === 'array') {
                    // 将文本框内容按行分割为数组
                    const value = paramInput.value.trim();
                    params[key] = value ? value.split('\n').filter(line => line.trim()) : [];
                } else if (type === 'object') {
                    // 尝试解析JSON
                    const value = paramInput.value.trim();
                    try {
                        params[key] = value ? JSON.parse(value) : {};
                    } catch (error) {
                        alert(`参数 ${key} 不是有效的JSON格式: ${error.message}`);
                        return;
                    }
                } else {
                    // 字符串类型
                    params[key] = paramInput.value.trim();
                }
                
                // 检查必填参数
                if (schema.required && (params[key] === null || params[key] === undefined || params[key] === '')) {
                    alert(`参数 ${key} 是必填的`);
                    return;
                }
            }
        }
        
        // 禁用提交按钮
        const callButton = document.getElementById('call-button');
        const originalButtonText = callButton.innerHTML;
        callButton.disabled = true;
        callButton.innerHTML = '<div class="loading-small"></div> 调用中...';
        
        try {
            // 调用工具
            const result = await callMCPTool(serverName, toolName, params);
            
            // 显示结果
            const resultElement = document.getElementById('tool-result');
            const resultContent = document.getElementById('result-content');
            
            resultElement.classList.remove('hidden');
            
            if (result.status === 'success') {
                // JSON格式化结果
                resultContent.textContent = JSON.stringify(result.result, null, 2);
                resultContent.className = 'text-sm whitespace-pre-wrap text-blue-800';
            } else {
                resultContent.textContent = `错误: ${result.message}`;
                resultContent.className = 'text-sm whitespace-pre-wrap text-red-600';
            }
        } catch (error) {
            // 显示错误
            const resultElement = document.getElementById('tool-result');
            const resultContent = document.getElementById('result-content');
            
            resultElement.classList.remove('hidden');
            resultContent.textContent = `错误: ${error.message}`;
            resultContent.className = 'text-sm whitespace-pre-wrap text-red-600';
        } finally {
            // 恢复按钮状态
            callButton.disabled = false;
            callButton.innerHTML = originalButtonText;
        }
    });
}

// 显示智能体配置界面
function showAgentConfig() {
    document.getElementById('mainChatArea').classList.add('hidden');
    document.getElementById('modelConfigArea').classList.add('hidden');
    document.getElementById('mcpConfigArea').classList.add('hidden');
    document.getElementById('agentConfigArea').classList.remove('hidden');
    
    // 加载可用模型到下拉菜单
    updateAgentModelSelect();
    
    // 加载可用工具
    updateAgentToolsContainer();
    
    // 加载已创建的智能体
    renderAgentList();
}

// 切换智能体表单显示
function toggleAgentForm() {
    const form = document.getElementById('agentForm');
    form.classList.toggle('hidden');
}

// 切换高级设置显示
function toggleAdvancedSettings() {
    const settings = document.getElementById('advancedSettings');
    const text = document.getElementById('advancedSettingsText');
    const icon = document.getElementById('advancedSettingsIcon');
    
    if (settings.classList.contains('hidden')) {
        settings.classList.remove('hidden');
        text.textContent = '隐藏';
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    } else {
        settings.classList.add('hidden');
        text.textContent = '显示';
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    }
}

// 更新温度值显示
function updateTemperatureValue() {
    const slider = document.getElementById('agentTemperature');
    const valueDisplay = document.getElementById('tempValue');
    
    if (slider && valueDisplay) {
        const value = slider.value / 100;
        valueDisplay.textContent = value.toFixed(1);
    }
}

// 更新智能体模型选择下拉菜单
function updateAgentModelSelect() {
    const select = document.getElementById('agentModel');
    
    if (!select) return;
    
    // 清空当前选项
    select.innerHTML = '<option value="">选择模型</option>';
    
    // 添加所有可用模型
    Object.keys(models).forEach(name => {
        const option = document.createElement('option');
        option.value = name;
        option.textContent = name;
        select.appendChild(option);
    });
}

// 更新智能体工具容器
function updateAgentToolsContainer() {
    const container = document.getElementById('agentToolsContainer');
    
    if (!container) return;
    
    // 清空当前内容
    container.innerHTML = '';
    
    // 如果没有MCP服务器，显示提示
    if (Object.keys(mcpServers).length === 0) {
        container.innerHTML = '<div class="col-span-3 text-center text-gray-500 py-2">未配置MCP服务器</div>';
        return;
    }
    
    // 为每个MCP服务器添加复选框
    Object.keys(mcpServers).forEach(name => {
        const healthStatus = mcpServerHealth[name] ? mcpServerHealth[name].status : 'unknown';
        const isHealthy = healthStatus === 'healthy';
        
        const checkbox = document.createElement('div');
        checkbox.className = 'flex items-center space-x-2 p-2 bg-gray-50 rounded-lg border border-gray-200 hover:border-blue-200 transition-all';
        
        checkbox.innerHTML = `
            <input type="checkbox" id="tool-${name}" name="agentTools" value="${name}" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                ${isHealthy ? '' : 'disabled'}>
            <label for="tool-${name}" class="flex flex-col flex-1 text-xs">
                <span class="font-medium text-gray-700">${name}</span>
                <span class="health-status ${healthStatus} text-xs mt-1 inline-flex items-center">
                    <i class="${getHealthStatusDisplay(healthStatus).icon} mr-1"></i>
                    ${isHealthy ? '可用' : '不可用'}
                </span>
            </label>
        `;
        
        container.appendChild(checkbox);
    });
}

// 创建新智能体
async function createAgent() {
    // 获取表单值
    const name = document.getElementById('agentName').value.trim();
    const description = document.getElementById('agentDescription').value.trim();
    const instructions = document.getElementById('agentInstructions').value.trim();
    const model = document.getElementById('agentModel').value;
    const maxSteps = document.getElementById('agentMaxSteps').value;
    const temperature = document.getElementById('agentTemperature').value / 100;
    const selfReflection = document.getElementById('agentSelfReflection').checked;
    
    // 获取选中的工具
    const selectedTools = [];
    document.querySelectorAll('input[name="agentTools"]:checked').forEach(checkbox => {
        selectedTools.push(checkbox.value);
    });
    
    // 验证必填字段
    if (!name) {
        showNotification('请输入智能体名称', 'error');
        return;
    }
    
    if (!model) {
        showNotification('请选择使用的模型', 'error');
        return;
    }
    
    if (!instructions) {
        showNotification('请输入智能体指令', 'error');
        return;
    }
    
    // 创建智能体配置
    const agent = {
        name,
        description,
        instructions,
        model,
        tools: selectedTools,
        maxSteps: parseInt(maxSteps),
        temperature,
        selfReflection,
        createdAt: new Date().toISOString()
    };
    
    try {
        // 保存智能体配置到后端
        const response = await fetch('/api/agents', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(agent)
        });
        
        if (response.ok) {
            // 更新本地智能体列表
            agents[name] = agent;
            showNotification('智能体创建成功', 'success');
            
            // 重置表单
            resetAgentForm();
            
            // 更新智能体列表
            renderAgentList();
            
            // 更新智能体选择下拉菜单
            updateAgentSelect();
        } else {
            const error = await response.json();
            showNotification(`创建失败: ${error.detail || '未知错误'}`, 'error');
        }
    } catch (error) {
        console.error('创建智能体失败:', error);
        showNotification(`创建失败: ${error.message}`, 'error');
    }
}

// 重置智能体表单
function resetAgentForm() {
    document.getElementById('agentName').value = '';
    document.getElementById('agentDescription').value = '';
    document.getElementById('agentInstructions').value = '';
    document.getElementById('agentModel').value = '';
    document.getElementById('agentMaxSteps').value = '5';
    document.getElementById('agentTemperature').value = '70';
    document.getElementById('tempValue').textContent = '0.7';
    document.getElementById('agentSelfReflection').checked = true;
    
    // 取消选中所有工具
    document.querySelectorAll('input[name="agentTools"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    // 隐藏表单
    toggleAgentForm();
}

// 加载智能体列表
async function loadAgents() {
    try {
        const response = await fetch('/api/agents');
        if (response.ok) {
            agents = await response.json();
            renderAgentList();
            updateAgentSelect();
        } else {
            console.error('加载智能体列表失败:', await response.text());
        }
    } catch (error) {
        console.error('加载智能体列表失败:', error);
    }
}

// 渲染智能体列表
function renderAgentList() {
    const list = document.getElementById('agentList');
    
    if (!list) return;
    
    // 清空当前列表
    list.innerHTML = '';
    
    // 如果没有智能体，显示提示
    if (Object.keys(agents).length === 0) {
        list.innerHTML = '<div class="text-center p-4 text-gray-500">暂无AI智能体配置</div>';
        return;
    }
    
    // 添加每个智能体卡片
    Object.entries(agents).forEach(([name, agent]) => {
        const item = document.createElement('div');
        item.className = 'config-item mb-3';
        
        const toolsText = agent.tools.length > 0 
            ? `${agent.tools.length}个工具` 
            : '无工具';
        
        item.innerHTML = `
            <div class="p-4 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition shadow-sm hover:shadow">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-robot text-white"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800">${agent.name}</div>
                            <div class="text-xs text-gray-500">${agent.description || '无描述'}</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="runAgent('${name}')" class="p-1.5 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-all" title="运行智能体">
                            <i class="fas fa-play"></i>
                        </button>
                        <button onclick="editAgent('${name}')" class="p-1.5 bg-gray-50 text-gray-600 rounded hover:bg-gray-100 transition-all" title="编辑智能体">
                            <i class="fas fa-pen"></i>
                        </button>
                        <button onclick="deleteAgent('${name}')" class="p-1.5 bg-red-50 text-red-600 rounded hover:bg-red-100 transition-all" title="删除智能体">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="mt-3 pt-3 border-t border-gray-100 grid grid-cols-3 gap-2 text-xs">
                    <div class="flex items-center">
                        <i class="fas fa-brain text-blue-500 mr-1.5"></i>
                        <span class="text-gray-600">使用模型: </span>
                        <span class="ml-1 font-medium">${agent.model}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-toolbox text-purple-500 mr-1.5"></i>
                        <span class="text-gray-600">工具: </span>
                        <span class="ml-1 font-medium">${toolsText}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-cog text-gray-500 mr-1.5"></i>
                        <span class="text-gray-600">温度: </span>
                        <span class="ml-1 font-medium">${agent.temperature.toFixed(1)}</span>
                    </div>
                </div>
            </div>
        `;
        
        list.appendChild(item);
    });
}

// 更新智能体选择下拉菜单
function updateAgentSelect() {
    const select = document.getElementById('selectedAgent');
    
    if (!select) return;
    
    // 保存当前选中值
    const currentValue = select.value;
    
    // 清空当前选项
    select.innerHTML = '<option value="">选择智能体</option>';
    
    // 添加所有可用智能体
    Object.keys(agents).forEach(name => {
        const option = document.createElement('option');
        option.value = name;
        option.textContent = name;
        select.appendChild(option);
    });
    
    // 恢复选中值
    if (currentValue && agents[currentValue]) {
        select.value = currentValue;
    }
}

// 编辑智能体
function editAgent(name) {
    const agent = agents[name];
    
    if (!agent) {
        showNotification('智能体不存在', 'error');
        return;
    }
    
    // 填充表单
    document.getElementById('agentName').value = agent.name;
    document.getElementById('agentDescription').value = agent.description || '';
    document.getElementById('agentInstructions').value = agent.instructions || '';
    document.getElementById('agentModel').value = agent.model || '';
    document.getElementById('agentMaxSteps').value = agent.maxSteps || 5;
    document.getElementById('agentTemperature').value = Math.round(agent.temperature * 100) || 70;
    document.getElementById('tempValue').textContent = agent.temperature.toFixed(1) || '0.7';
    document.getElementById('agentSelfReflection').checked = agent.selfReflection !== false;
    
    // 选中相应工具
    document.querySelectorAll('input[name="agentTools"]').forEach(checkbox => {
        checkbox.checked = agent.tools.includes(checkbox.value);
    });
    
    // 显示表单
    const form = document.getElementById('agentForm');
    if (form.classList.contains('hidden')) {
        toggleAgentForm();
    }
    
    // 显示高级设置
    const advancedSettings = document.getElementById('advancedSettings');
    if (advancedSettings.classList.contains('hidden')) {
        toggleAdvancedSettings();
    }
}

// 删除智能体
async function deleteAgent(name) {
    if (!confirm(`确定要删除智能体 "${name}" 吗？`)) {
        return;
    }
    
    try {
        const response = await fetch(`/api/agents/${name}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            // 从本地列表中删除
            delete agents[name];
            showNotification('智能体已删除', 'success');
            
            // 更新列表显示
            renderAgentList();
            updateAgentSelect();
        } else {
            const error = await response.json();
            showNotification(`删除失败: ${error.detail || '未知错误'}`, 'error');
        }
    } catch (error) {
        console.error('删除智能体失败:', error);
        showNotification(`删除失败: ${error.message}`, 'error');
    }
}

// 运行智能体
async function runAgent(name) {
    const agent = agents[name];
    
    if (!agent) {
        showNotification('智能体不存在', 'error');
        return;
    }
    
    // 获取用户输入
    const message = document.getElementById('messageInput').value.trim();
    
    if (!message) {
        showNotification('请输入消息', 'warning');
        return;
    }
    
    // 添加用户消息到聊天界面
    appendChatMessage('user', message);
    
    // 清空输入框并禁用发送按钮
    document.getElementById('messageInput').value = '';
    document.getElementById('sendButton').disabled = true;
    
    // 显示AI回复的占位符
    const assistantMessageId = 'assistant-message-' + Date.now();
    const assistantContentId = 'assistant-content-' + Date.now();
    
    // 创建助手消息元素
    const assistantDiv = document.createElement('div');
    assistantDiv.id = assistantMessageId;
    assistantDiv.className = `message assistant-message mb-4 p-3 rounded-lg bg-gray-50`;
    
    // 添加角色标签
    const roleLabel = document.createElement('div');
    roleLabel.className = 'font-medium text-xs text-gray-500 mb-1';
    roleLabel.textContent = `AI (${agent.name})`;
    
    // 创建消息内容
    const contentDiv = document.createElement('div');
    contentDiv.id = assistantContentId;
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = '<div class="typing-indicator"><span></span><span></span><span></span></div>';
    
    // 构建消息
    assistantDiv.appendChild(roleLabel);
    assistantDiv.appendChild(contentDiv);
    document.getElementById('chatContainer').appendChild(assistantDiv);
    scrollChatToBottom();
    
    // 显示思考面板
    showThinkingPanel(agent);
    
    try {
        // 调用智能体API
        const response = await fetch('/api/agents/run', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                agent_name: name,
                message: message,
                stream: true
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        // 获取响应流
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        let responseText = '';
        let thinking = [];
        
        // 读取流数据
        while (true) {
            const { value, done } = await reader.read();
            
            if (done) {
                break;
            }
            
            // 解码数据
            const chunk = decoder.decode(value, { stream: true });
            
            // 处理数据块 (每个块应该是一个JSON对象)
            const lines = chunk.split('\n').filter(line => line.trim());
            
            for (const line of lines) {
                try {
                    const data = JSON.parse(line);
                    
                    if (data.type === 'thinking') {
                        // 更新思考过程
                        thinking.push(data);
                        updateThinkingPanel(data, thinking.length);
                    } else if (data.type === 'response') {
                        // 更新响应文本
                        responseText += data.content || '';
                        contentDiv.innerHTML = formatMessageContent(responseText);
                        scrollChatToBottom();
                    } else if (data.type === 'done') {
                        // 流结束
                        console.log('智能体处理完成');
                    }
                } catch (e) {
                    console.error('解析数据块失败:', e, line);
                }
            }
        }
    } catch (error) {
        console.error('智能体运行失败:', error);
        
        // 显示错误消息
        contentDiv.innerHTML = `<div class="text-red-500">运行失败: ${error.message}</div>`;
    } finally {
        // 启用发送按钮
        document.getElementById('sendButton').disabled = false;
        scrollChatToBottom();
    }
}

// 显示思考面板
function showThinkingPanel(agent) {
    // 清空当前思考内容
    currentAgentThinking = [];
    
    // 更新面板标题
    document.getElementById('agentThinkingName').textContent = `${agent.name} 的思考过程`;
    document.getElementById('agentThinkingStep').textContent = '步骤 0/0';
    
    // 清空内容
    document.getElementById('agentThinkingContent').innerHTML = `
        <div class="p-3 border-l-4 border-blue-400 bg-blue-50 text-sm">
            <div class="font-medium text-blue-700 mb-1">思考中...</div>
            <div class="text-blue-600 text-xs">智能体正在分析您的问题</div>
        </div>
    `;
    
    // 显示面板
    document.getElementById('agentThinkingPanel').classList.remove('hidden');
}

// 更新思考面板
function updateThinkingPanel(thinkingData, stepNumber) {
    const container = document.getElementById('agentThinkingContent');
    const totalSteps = document.getElementById('agentThinkingStep');
    
    // 更新步骤数
    totalSteps.textContent = `步骤 ${stepNumber}/${thinkingData.max_steps || '?'}`;
    
    // 清空内容
    if (stepNumber === 1) {
        container.innerHTML = '';
    }
    
    // 创建思考步骤元素
    const step = document.createElement('div');
    step.className = 'mb-3 p-3 border border-gray-200 rounded-lg';
    
    // 根据类型设置不同样式
    let icon, title, contentClass;
    
    switch (thinkingData.step_type) {
        case 'analyze':
            icon = 'fa-search';
            title = '分析问题';
            contentClass = 'text-gray-700';
            break;
        case 'plan':
            icon = 'fa-list-check';
            title = '规划步骤';
            contentClass = 'text-indigo-700';
            break;
        case 'reflect':
            icon = 'fa-lightbulb';
            title = '自我反思';
            contentClass = 'text-amber-700';
            break;
        case 'tool':
            icon = 'fa-wrench';
            title = '使用工具';
            contentClass = 'text-purple-700';
            break;
        case 'action':
            icon = 'fa-bolt';
            title = '执行操作';
            contentClass = 'text-green-700';
            break;
        default:
            icon = 'fa-circle-info';
            title = '思考';
            contentClass = 'text-gray-700';
    }
    
    // 构建HTML
    step.innerHTML = `
        <div class="flex items-center mb-2">
            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <i class="fas ${icon} text-blue-600 text-xs"></i>
            </div>
            <div class="font-medium text-sm">${title}</div>
            <div class="ml-auto text-xs text-gray-500">${formatThinkingTime(thinkingData.timestamp)}</div>
        </div>
        <div class="${contentClass} text-sm whitespace-pre-wrap">${thinkingData.content}</div>
    `;
    
    // 添加到容器
    container.appendChild(step);
    
    // 滚动到底部
    container.scrollTop = container.scrollHeight;
}

// 格式化思考时间
function formatThinkingTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
}

// 切换思考面板显示
function toggleThinkingPanel() {
    const panel = document.getElementById('agentThinkingPanel');
    const btn = document.getElementById('collapseThinkingBtn');
    
    if (panel.classList.contains('hidden')) {
        panel.classList.remove('hidden');
        btn.innerHTML = '<i class="fas fa-chevron-up mr-1"></i>隐藏';
    } else {
        panel.classList.add('hidden');
        btn.innerHTML = '<i class="fas fa-chevron-down mr-1"></i>显示';
    }
}

// 清除智能体思考内容
function clearAgentThinking() {
    const container = document.getElementById('agentThinkingContent');
    
    if (container) {
        container.innerHTML = `
            <div class="text-center text-gray-500 py-4">
                <i class="fas fa-robot text-blue-300 text-3xl mb-2"></i>
                <p>智能体尚未开始思考</p>
            </div>
        `;
    }
    
    // 重置步骤计数
    document.getElementById('agentThinkingStep').textContent = '步骤 0/0';
}

// 通用页面切换函数 - 用于确保所有页面切换操作都使用相同的方法
function switchToPage(pageId, highlightItem = null) {
    try {
        console.log(`尝试切换到页面: ${pageId}`);
        
        // 检查目标页面是否存在
        const targetPage = document.getElementById(pageId);
        if (!targetPage) {
            console.error(`目标页面不存在: ${pageId}`);
            showNotification(`页面 "${pageId}" 不存在，请刷新浏览器或联系管理员`, 'error');
            return false;
        }
        
        // 定义所有可能的页面
        const pages = [
            'chatContainer',
            'modelConfigArea',
            'mcpConfigArea',
            'agentConfigArea',
            'settingsArea'
        ];
        
        // 隐藏所有页面
        pages.forEach(page => {
            const element = document.getElementById(page);
            if (element) {
                element.classList.add('hidden');
            } else {
                console.warn(`页面元素不存在: ${page}`);
            }
        });
        
        // 显示目标页面
        targetPage.classList.remove('hidden');
        
        // 如果提供了高亮项，更新侧边栏高亮
        if (highlightItem) {
            updateSidebarHighlight(highlightItem);
        }
        
        console.log(`成功切换到页面: ${pageId}`);
        return true; // 页面切换成功
    } catch (error) {
        console.error('页面切换错误:', error);
        showNotification('页面切换出错: ' + error.message, 'error');
        return false;
    }
}

// 界面切换函数
function showMainChat() {
    if (switchToPage('chatContainer', 'chat')) {
        // 如果有其他初始化操作，可以放在这里
    }
}

function showModelConfig() {
    if (switchToPage('modelConfigArea', 'model')) {
        loadModels(); // 加载模型列表
    }
}

function showMCPConfig() {
    if (switchToPage('mcpConfigArea', 'mcp')) {
        loadMCPServers(); // 加载MCP服务器列表
    }
}

function showAgentConfig() {
    if (switchToPage('agentConfigArea', 'agent')) {
        // 加载可用模型到下拉菜单
        updateAgentModelSelect();
        
        // 加载可用工具
        updateAgentToolsContainer();
        
        // 加载已创建的智能体
        renderAgentList();
    }
}

// 显示设置页面
function showSettings() {
    if (switchToPage('settingsArea')) {
        // 默认显示常规设置选项卡
        showSettingsTab('general');
    }
}

// 关闭设置页面
function closeSettings() {
    switchToPage('chatContainer');
}

// 切换设置页面的选项卡
function showSettingsTab(tabName) {
    try {
        // 隐藏所有选项卡
        const tabs = document.querySelectorAll('.settings-tab');
        tabs.forEach(tab => {
            tab.classList.add('hidden');
        });
        
        // 取消所有选项卡按钮的高亮
        const tabButtons = document.querySelectorAll('[onclick^="showSettingsTab"]');
        tabButtons.forEach(btn => {
            btn.classList.remove('bg-blue-50', 'text-blue-700');
            btn.classList.add('text-gray-700', 'hover:bg-gray-50');
        });
        
        // 显示选中的选项卡
        const selectedTab = document.getElementById(tabName + 'Settings');
        if (selectedTab) {
            selectedTab.classList.remove('hidden');
        } else {
            console.warn(`设置选项卡 ${tabName} 不存在`);
        }
        
        // 高亮选中的选项卡按钮
        const selectedBtn = document.querySelector(`[onclick="showSettingsTab('${tabName}')"]`);
        if (selectedBtn) {
            selectedBtn.classList.remove('text-gray-700', 'hover:bg-gray-50');
            selectedBtn.classList.add('bg-blue-50', 'text-blue-700', 'font-medium');
        }
    } catch (error) {
        console.error('切换设置选项卡错误:', error);
    }
}

// 隐藏所有内容区域
function hideAllAreas() {
    const areas = [
        'chatContainer',
        'modelConfigArea',
        'mcpConfigArea',
        'agentConfigArea',
        'settingsArea'
    ];
    
    areas.forEach(area => {
        const element = document.getElementById(area);
        if (element) {
            element.classList.add('hidden');
        }
    });
}

// 更新侧边栏高亮状态
function updateSidebarHighlight(activeItem) {
    // 移除所有高亮
    document.querySelectorAll('.sidebar-item').forEach(item => {
        item.classList.remove('bg-indigo-50', 'text-indigo-600', 'bg-emerald-50', 'text-emerald-600', 'bg-purple-50', 'text-purple-600', 'bg-blue-50', 'text-blue-600');
        item.classList.add('text-gray-700');
    });
    
    // 添加对应高亮
    const activeButton = document.querySelector(`[data-item="${activeItem}"]`);
    if (activeButton) {
        activeButton.classList.remove('text-gray-700');
        
        switch (activeItem) {
            case 'chat':
                activeButton.classList.add('bg-indigo-50', 'text-indigo-600');
                break;
            case 'model':
                activeButton.classList.add('bg-emerald-50', 'text-emerald-600');
                break;
            case 'mcp':
                activeButton.classList.add('bg-purple-50', 'text-purple-600');
                break;
            case 'agent':
                activeButton.classList.add('bg-blue-50', 'text-blue-600');
                break;
        }
    }
}

// 保存设置
function saveSettings() {
    // 收集设置数据
    const settings = {
        general: {
            language: document.querySelector('select[name="language"]')?.value || 'zh',
            soundNotification: document.getElementById('soundNotification')?.checked || false,
            autoSave: document.getElementById('autoSave')?.checked || true,
            autoClear: document.querySelector('select[name="autoClear"]')?.value || 'never'
        },
        appearance: {
            theme: document.querySelector('input[name="theme"]:checked')?.value || 'auto',
            accentColor: document.querySelector('input[name="accentColor"]:checked')?.value || 'indigo',
            fontSize: document.querySelector('input[type="range"]')?.value || 16
        }
        // 其他设置项...
    };
    
    // 保存到本地存储
    localStorage.setItem('nexusai_settings', JSON.stringify(settings));
    
    // 应用设置
    applySettings(settings);
    
    // 显示成功通知
    showNotification('设置已保存', 'success');
}

// 应用设置
function applySettings(settings) {
    // 应用主题
    if (settings.appearance?.theme === 'dark') {
        enableDarkMode();
    } else if (settings.appearance?.theme === 'light') {
        disableDarkMode();
    } else {
        // 跟随系统
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            enableDarkMode();
        } else {
            disableDarkMode();
        }
    }
    
    // 应用字体大小
    if (settings.appearance?.fontSize) {
        document.documentElement.style.fontSize = `${settings.appearance.fontSize}px`;
    }
    
    // 应用其他设置...
}

// 加载设置
function loadSettings() {
    const savedSettings = localStorage.getItem('nexusai_settings');
    
    if (savedSettings) {
        try {
            const settings = JSON.parse(savedSettings);
            
            // 填充设置表单
            if (settings.general) {
                if (settings.general.language) {
                    const langSelect = document.querySelector('select[name="language"]');
                    if (langSelect) langSelect.value = settings.general.language;
                }
                
                if ('soundNotification' in settings.general) {
                    const soundCheck = document.getElementById('soundNotification');
                    if (soundCheck) soundCheck.checked = settings.general.soundNotification;
                }
                
                if ('autoSave' in settings.general) {
                    const autoSaveCheck = document.getElementById('autoSave');
                    if (autoSaveCheck) autoSaveCheck.checked = settings.general.autoSave;
                }
                
                if (settings.general.autoClear) {
                    const clearSelect = document.querySelector('select[name="autoClear"]');
                    if (clearSelect) clearSelect.value = settings.general.autoClear;
                }
            }
            
            if (settings.appearance) {
                if (settings.appearance.theme) {
                    const themeRadio = document.querySelector(`input[name="theme"][value="${settings.appearance.theme}"]`);
                    if (themeRadio) themeRadio.checked = true;
                }
                
                if (settings.appearance.accentColor) {
                    const colorRadio = document.querySelector(`input[name="accentColor"][value="${settings.appearance.accentColor}"]`);
                    if (colorRadio) colorRadio.checked = true;
                }
                
                if (settings.appearance.fontSize) {
                    const fontRange = document.querySelector('input[type="range"]');
                    if (fontRange) fontRange.value = settings.appearance.fontSize;
                }
            }
            
            // 应用设置
            applySettings(settings);
            
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }
}

// 恢复默认设置
function resetSettings() {
    // 确认提示
    if (!confirm('确定要恢复所有设置为默认值吗？这将覆盖您的自定义设置。')) {
        return;
    }
    
    // 默认设置
    const defaultSettings = {
        general: {
            language: 'zh',
            soundNotification: false,
            autoSave: true,
            autoClear: 'never'
        },
        appearance: {
            theme: 'auto',
            accentColor: 'indigo',
            fontSize: 16
        }
        // 其他默认设置...
    };
    
    // 保存到本地存储
    localStorage.setItem('nexusai_settings', JSON.stringify(defaultSettings));
    
    // 应用默认设置
    applySettings(defaultSettings);
    
    // 更新表单
    if (defaultSettings.general) {
        if (defaultSettings.general.language) {
            const langSelect = document.querySelector('select[name="language"]');
            if (langSelect) langSelect.value = defaultSettings.general.language;
        }
        
        if ('soundNotification' in defaultSettings.general) {
            const soundCheck = document.getElementById('soundNotification');
            if (soundCheck) soundCheck.checked = defaultSettings.general.soundNotification;
        }
        
        if ('autoSave' in defaultSettings.general) {
            const autoSaveCheck = document.getElementById('autoSave');
            if (autoSaveCheck) autoSaveCheck.checked = defaultSettings.general.autoSave;
        }
        
        if (defaultSettings.general.autoClear) {
            const clearSelect = document.querySelector('select[name="autoClear"]');
            if (clearSelect) clearSelect.value = defaultSettings.general.autoClear;
        }
    }
    
    if (defaultSettings.appearance) {
        if (defaultSettings.appearance.theme) {
            const themeRadio = document.querySelector(`input[name="theme"][value="${defaultSettings.appearance.theme}"]`);
            if (themeRadio) themeRadio.checked = true;
        }
        
        if (defaultSettings.appearance.accentColor) {
            const colorRadio = document.querySelector(`input[name="accentColor"][value="${defaultSettings.appearance.accentColor}"]`);
            if (colorRadio) colorRadio.checked = true;
        }
        
        if (defaultSettings.appearance.fontSize) {
            const fontRange = document.querySelector('input[type="range"]');
            if (fontRange) fontRange.value = defaultSettings.appearance.fontSize;
        }
    }
    
    // 显示成功通知
    showNotification('已恢复默认设置', 'success');
}