"""
MCP (Model Context Protocol) client utilities
"""
import asyncio
import json
import logging
import subprocess
import os
import platform
import sys
import socket
from typing import Dict, List, Any, Optional, Union

logger = logging.getLogger(__name__)

# Import HTTP libraries with fallbacks
try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    logger.warning("aiohttp not available - falling back to requests")
    AIOHTTP_AVAILABLE = False

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    logger.warning("requests not available")
    REQUESTS_AVAILABLE = False

class DirectMCPClient:
    """Direct implementation of MCP client without depending on mcp-agent package"""
    
    def __init__(self, server_name: str, server_config: Dict[str, Any]):
        """Initialize the MCP client with server configuration"""
        self.server_name = server_name
        self.config = server_config
        self.connection_type = server_config.get("connection_type", "stdio")
        self.process = None
        self.tools_cache = None
        
        # For HTTP-based connections
        self.base_url = server_config.get("url", "")
        self.headers = server_config.get("headers", {})
        
        # For stdio connections
        self.command = server_config.get("command", [])
        self.args = server_config.get("args", [])
        self.env = server_config.get("env", {})
        
        logger.info(f"Initialized MCP client for {server_name} with connection type: {self.connection_type}")
    
    async def get_tools(self) -> List[Dict[str, Any]]:
        """Get the list of tools from the MCP server"""
        if self.connection_type == "stdio":
            return await self._get_tools_stdio()
        elif self.connection_type in ["sse", "streamableHttp"]:
            return await self._get_tools_http()
        else:
            logger.error(f"Unsupported connection type: {self.connection_type}")
            return []
    
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on the MCP server"""
        if self.connection_type == "stdio":
            return await self._call_tool_stdio(tool_name, params)
        elif self.connection_type in ["sse", "streamableHttp"]:
            return await self._call_tool_http(tool_name, params)
        else:
            logger.error(f"Unsupported connection type: {self.connection_type}")
            return {"error": f"不支持的连接类型: {self.connection_type}"}
    
    async def _get_tools_stdio(self) -> List[Dict[str, Any]]:
        """Get tools from a stdio-based MCP server"""
        try:
            # Start the server process if not running
            if not self.process:
                await self._start_process()
            
            # Send a tools request
            request = {"type": "list_tools"}
            await self._write_to_process(json.dumps(request))
            
            # Read the response
            response_text = await self._read_from_process()
            response = json.loads(response_text)
            
            return response.get("tools", [])
        except Exception as e:
            logger.error(f"Error getting tools via stdio: {e}")
            return []
    
    async def _call_tool_stdio(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on a stdio-based MCP server"""
        try:
            # Start the server process if not running
            if not self.process:
                await self._start_process()
            
            # Send a tool call request
            request = {
                "type": "tool_call",
                "tool": tool_name,
                "params": params
            }
            await self._write_to_process(json.dumps(request))
            
            # Read the response
            response_text = await self._read_from_process()
            return json.loads(response_text)
        except Exception as e:
            logger.error(f"Error calling tool via stdio: {e}")
            return {"error": f"工具调用失败: {str(e)}"}
    
    async def _get_tools_http(self) -> List[Dict[str, Any]]:
        """Get tools from an HTTP-based MCP server"""
        try:
            tools_url = f"{self.base_url}/tools"
            
            if AIOHTTP_AVAILABLE:
                async with aiohttp.ClientSession() as session:
                    async with session.get(tools_url, headers=self.headers) as response:
                        if response.status == 200:
                            data = await response.json()
                            return data.get("tools", [])
                        else:
                            logger.error(f"HTTP error getting tools: {response.status}")
                            return []
            elif REQUESTS_AVAILABLE:
                import requests
                response = requests.get(tools_url, headers=self.headers)
                if response.status_code == 200:
                    data = response.json()
                    return data.get("tools", [])
                else:
                    logger.error(f"HTTP error getting tools: {response.status_code}")
                    return []
            else:
                logger.error("No HTTP library available")
                return []
        except Exception as e:
            logger.error(f"Error getting tools via HTTP: {e}")
            return []
    
    async def _call_tool_http(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on an HTTP-based MCP server"""
        try:
            call_url = f"{self.base_url}/call"
            payload = {
                "tool": tool_name,
                "params": params
            }
            
            if AIOHTTP_AVAILABLE:
                async with aiohttp.ClientSession() as session:
                    async with session.post(call_url, json=payload, headers=self.headers) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            logger.error(f"HTTP error calling tool: {response.status}")
                            return {"error": f"HTTP错误: {response.status}"}
            elif REQUESTS_AVAILABLE:
                import requests
                response = requests.post(call_url, json=payload, headers=self.headers)
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"HTTP error calling tool: {response.status_code}")
                    return {"error": f"HTTP错误: {response.status_code}"}
            else:
                logger.error("No HTTP library available")
                return {"error": "没有可用的HTTP库"}
        except Exception as e:
            logger.error(f"Error calling tool via HTTP: {e}")
            return {"error": f"工具调用失败: {str(e)}"}
    
    async def _start_process(self):
        """Start the MCP server process"""
        try:
            # Build the command
            cmd = self.command + self.args
            
            # Set up environment
            env = os.environ.copy()
            env.update(self.env)
            
            # Start the process
            self.process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env
            )
            
            logger.info(f"Started MCP server process: {' '.join(cmd)}")
        except Exception as e:
            logger.error(f"Failed to start MCP server process: {e}")
            raise
    
    async def _write_to_process(self, data: str):
        """Write data to the process stdin"""
        if self.process and self.process.stdin:
            self.process.stdin.write(data.encode() + b'\n')
            await self.process.stdin.drain()
    
    async def _read_from_process(self) -> str:
        """Read data from the process stdout"""
        if self.process and self.process.stdout:
            line = await self.process.stdout.readline()
            return line.decode().strip()
        return ""
    
    async def close(self):
        """Close the MCP client and cleanup resources"""
        if self.process:
            try:
                self.process.terminate()
                await self.process.wait()
            except Exception as e:
                logger.error(f"Error closing MCP process: {e}")
            finally:
                self.process = None
