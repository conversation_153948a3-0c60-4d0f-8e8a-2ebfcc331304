# 示例配置文件 - 复制并重命名为 config.yaml 来使用
models:
  "GPT-4":
    provider: "openai"
    api_key: "your_openai_api_key_here"
    api_url: "https://api.openai.com/v1"
    model_name: "gpt-4"
  
  "Claude-3":
    provider: "anthropic"
    api_key: "your_anthropic_api_key_here"
    api_url: "https://api.anthropic.com"
    model_name: "claude-3-haiku-20240307"

  "DeepSeek Chat":
    provider: "deepseek"
    api_key: "your_deepseek_api_key_here"
    api_url: "https://api.deepseek.com/v1"
    model_name: "deepseek-chat"

  "通义千问":
    provider: "qwen"
    api_key: "your_qwen_api_key_here"
    api_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
    model_name: "qwen-turbo"

mcp_servers:
  "fetch":
    command: "uvx"
    args: ["mcp-server-fetch"]
    description: "Fetch content at URLs from the world wide web"
  
  "filesystem":
    command: "uvx"
    args: ["mcp-server-filesystem", "--allowed-dirs", "."]
    description: "File system operations"
  
  "sqlite":
    command: "uvx"
    args: ["mcp-server-sqlite", "--db-path", "./data.db"]
    description: "SQLite database operations"

chat_history: [] 