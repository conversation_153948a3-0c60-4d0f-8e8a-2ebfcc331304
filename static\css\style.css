/* 自定义样式 */
.chat-message {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 消息气泡样式 */
.user-message {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 18px 18px 6px 18px;
    margin-left: auto;
    max-width: 70%;
    padding: 12px 16px;
    word-wrap: break-word;
}

.assistant-message {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 18px 18px 18px 6px;
    margin-right: auto;
    max-width: 70%;
    padding: 12px 16px;
    word-wrap: break-word;
}

/* 配置项样式 */
.config-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: between;
    transition: all 0.2s ease;
}

.config-item:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.config-item .info {
    flex: 1;
}

.config-item .name {
    font-weight: 600;
    color: #374151;
}

.config-item .details {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 2px;
}

.config-item .actions {
    display: flex;
    gap: 8px;
}

/* 按钮样式 */
.btn-sm {
    padding: 4px 8px;
    font-size: 0.75rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 复选框样式 */
.mcp-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    background: white;
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 20px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mcp-checkbox:hover {
    border-color: #9ca3af;
}

.mcp-checkbox.active {
    background: #dbeafe;
    border-color: #3b82f6;
    color: #1d4ed8;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .w-80 {
        width: 100%;
        position: fixed;
        top: 0;
        left: -100%;
        height: 100%;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .w-80.active {
        left: 0;
    }
    
    .user-message,
    .assistant-message {
        max-width: 85%;
    }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 24px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
}

.notification.success {
    background-color: #10b981;
}

.notification.error {
    background-color: #ef4444;
}

.notification.info {
    background-color: #3b82f6;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 代码块样式 */
.message-content pre {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px;
    overflow-x: auto;
    margin: 8px 0;
}

.message-content code {
    background: #f1f5f9;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

/* 健康状态指示器 */
.health-status {
    display: inline-flex;
    align-items: center;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

.health-status.healthy {
    background-color: #d1fae5;
    color: #065f46;
}

.health-status.warning {
    background-color: #fef3c7;
    color: #92400e;
}

.health-status.error {
    background-color: #fee2e2;
    color: #991b1b;
}

.health-status.unknown {
    background-color: #f3f4f6;
    color: #6b7280;
}

.health-status i {
    margin-right: 4px;
    font-size: 0.7rem;
}

/* 服务器状态详情 */
.server-status-details {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 8px;
    margin-top: 8px;
    font-size: 0.75rem;
}

.server-status-details .status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.server-status-details .status-item:last-child {
    margin-bottom: 0;
}

.status-item .label {
    color: #6b7280;
    font-weight: 500;
}

.status-item .value {
    color: #374151;
}

/* 健康检查按钮 */
.health-check-btn {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.health-check-btn:hover {
    transform: scale(1.02);
}

.health-check-btn i {
    margin-right: 3px;
}

/* 加载动画 - 小尺寸 */
.loading-small {
    width: 12px;
    height: 12px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* MCP服务器列表增强 */
.config-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s;
}

.config-item:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-item .info {
    flex: 1;
}

.config-item .name {
    font-weight: 600;
    color: #374151;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.config-item .details {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 2px;
}

.config-item .actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.75rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.btn-danger:hover {
    background-color: #fecaca;
}

/* MCP服务器选择器 */
.mcp-checkbox {
    display: inline-flex;
    align-items: center;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
    margin: 2px;
}

.mcp-checkbox:hover {
    border-color: #9ca3af;
    background-color: #f9fafb;
}

.mcp-checkbox.active {
    background-color: #dbeafe;
    border-color: #3b82f6;
    color: #1e40af;
}

.mcp-checkbox input {
    margin-right: 6px;
}

/* 聊天界面 */
.chat-message {
    max-width: 80%;
    margin-bottom: 16px;
    animation: slideIn 0.3s ease-out;
}

.user-message {
    align-self: flex-end;
    margin-left: auto;
}

.assistant-message {
    align-self: flex-start;
}

.message-content {
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
}

.user-message .message-content {
    background-color: #3b82f6;
    color: white;
}

.assistant-message .message-content {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
}

/* 代码块样式 */
.message-content pre {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 8px 0;
}

.message-content code {
    background-color: #f3f4f6;
    color: #ef4444;
    padding: 2px 4px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.assistant-message .message-content code {
    background-color: #e5e7eb;
}

/* 动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 加载动画 */
.loading {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background-color: #10b981;
}

.notification.error {
    background-color: #ef4444;
}

.notification.info {
    background-color: #3b82f6;
}

.notification.warning {
    background-color: #f59e0b;
}

/* 基础样式 */
:root {
    --primary: #4f46e5;
    --primary-dark: #4338ca;
    --primary-light: #818cf8;
    --secondary: #7c3aed;
    --accent: #06b6d4;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --background: #f9fafb;
    --foreground: #ffffff;
    --text: #1f2937;
    --text-secondary: #6b7280;
    --border: #e5e7eb;
    --border-hover: #d1d5db;
    --shadow: rgba(0, 0, 0, 0.05);
    --shadow-lg: rgba(0, 0, 0, 0.1);
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 暗色主题变量 */
.dark-theme {
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --secondary: #8b5cf6;
    --accent: #0ea5e9;
    --success: #22c55e;
    --warning: #f97316;
    --danger: #ef4444;
    --background: #111827;
    --foreground: #1f2937;
    --text: #f9fafb;
    --text-secondary: #9ca3af;
    --border: #374151;
    --border-hover: #4b5563;
    --shadow: rgba(0, 0, 0, 0.3);
    --shadow-lg: rgba(0, 0, 0, 0.5);
}

body {
    font-family: var(--font-family);
    color: var(--text);
    background-color: var(--background);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 通用卡片样式 */
.config-item {
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px var(--shadow);
}

.config-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px var(--shadow-lg);
}

/* 服务器列表项 */
.config-item .server-details {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
}

/* 健康状态指示器 */
.health-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.health-status.healthy {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.health-status.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.health-status.error {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.health-status.unknown {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

/* 操作按钮 */
.health-check-btn {
    width: 1.75rem;
    height: 1.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    background-color: #f3f4f6;
    color: #6b7280;
    transition: all 0.2s ease;
}

.health-check-btn:hover {
    background-color: #e5e7eb;
    color: #4b5563;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
}

.btn-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.btn-danger:hover {
    background-color: rgba(239, 68, 68, 0.2);
}

/* MCP复选框 */
.mcp-checkbox {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 0.875rem;
    background-color: var(--foreground);
}

.mcp-checkbox.active {
    background-color: rgba(99, 102, 241, 0.1);
    border-color: #6366f1;
}

.mcp-checkbox:hover {
    border-color: var(--border-hover);
}

/* 表单样式 */
input, select, textarea {
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    padding: 0.5rem 0.75rem;
    background-color: var(--foreground);
    color: var(--text);
    transition: all 0.2s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 消息通知 */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: var(--foreground);
    box-shadow: 0 4px 12px var(--shadow-lg);
    z-index: 100;
    transform: translateX(150%);
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    max-width: 24rem;
    border-left: 4px solid var(--primary);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: var(--success);
}

.notification.error {
    border-left-color: var(--danger);
}

.notification.warning {
    border-left-color: var(--warning);
}

/* 加载动画 */
.loading-dots {
    display: inline-flex;
    align-items: center;
}

.loading-dots span {
    width: 0.5rem;
    height: 0.5rem;
    margin: 0 0.125rem;
    background-color: var(--primary);
    border-radius: 50%;
    display: inline-block;
    animation: pulse 1.4s infinite cubic-bezier(0.455, 0.03, 0.515, 0.955) both;
}

.loading-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes pulse {
    0%, 80%, 100% {
        transform: scale(0.6);
        opacity: 0.6;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 现代加载动画 */
.loading-spinner {
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid rgba(99, 102, 241, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 消息样式 */
.message {
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 1rem;
    animation: slideIn 0.3s ease;
    box-shadow: 0 2px 8px var(--shadow);
}

.user-message {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    margin-left: 2rem;
    margin-right: 0;
    border-bottom-right-radius: 0;
}

.assistant-message {
    background-color: var(--foreground);
    border: 1px solid var(--border);
    margin-right: 2rem;
    margin-left: 0;
    border-bottom-left-radius: 0;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(1rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 代码块样式 */
pre {
    background-color: #1e293b;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.75rem 0;
    position: relative;
}

pre::before {
    content: attr(data-language);
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.75rem;
    color: #64748b;
    text-transform: uppercase;
}

code {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: var(--border);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--border-hover);
}

/* 玻璃态模糊效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .glass-effect {
    background: rgba(31, 41, 55, 0.4);
    border-color: rgba(255, 255, 255, 0.05);
}

/* 响应式布局优化 */
@media (max-width: 768px) {
    .config-item {
        margin-bottom: 0.75rem;
    }
    
    .w-80 {
        width: 100%;
        max-width: 100%;
        position: fixed;
        z-index: 40;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .w-80.open {
        transform: translateX(0);
    }
}

/* 工具提示 */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--text);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 10;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 6px var(--shadow);
} 