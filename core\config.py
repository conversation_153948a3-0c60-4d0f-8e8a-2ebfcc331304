"""
Configuration management for the AI Assistant and MCP Marketplace
"""
import os
import yaml
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

# Default configuration path
CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.yaml")

def load_config(config_path=CONFIG_PATH) -> Dict[str, Any]:
    """
    Load configuration from a YAML file
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Dictionary containing the configuration
    """
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        else:
            logger.warning(f"Configuration file not found at {config_path}, using default configuration")
            return {}
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}

def save_config(config: Dict[str, Any], config_path=CONFIG_PATH) -> bool:
    """
    Save configuration to a YAML file
    
    Args:
        config: Configuration dictionary to save
        config_path: Path to save the configuration file
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        return True
    except Exception as e:
        logger.error(f"Error saving configuration: {e}")
        return False 