#!/usr/bin/env python
"""
清理插件目录和环境的脚本
"""
import os
import shutil
import logging
import argparse
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clean_plugin_dirs(plugins_dir):
    """
    清理插件目录
    
    Args:
        plugins_dir: 插件主目录路径
    
    Returns:
        int: 清理的插件数量
    """
    try:
        # 检查目录是否存在
        if not os.path.exists(plugins_dir):
            logger.error(f"插件目录不存在: {plugins_dir}")
            return 0
        
        # 获取所有插件目录
        plugin_dirs = [d for d in os.listdir(plugins_dir) 
                       if os.path.isdir(os.path.join(plugins_dir, d))]
        
        if not plugin_dirs:
            logger.info(f"没有找到插件目录: {plugins_dir}")
            return 0
        
        logger.info(f"找到 {len(plugin_dirs)} 个插件目录")
        
        # 删除所有插件目录
        count = 0
        for plugin_dir in plugin_dirs:
            dir_path = os.path.join(plugins_dir, plugin_dir)
            try:
                logger.info(f"删除插件目录: {dir_path}")
                shutil.rmtree(dir_path)
                count += 1
            except Exception as e:
                logger.error(f"删除插件目录时出错 {dir_path}: {str(e)}")
        
        return count
    except Exception as e:
        logger.error(f"清理插件目录时出错: {str(e)}")
        return 0

def clean_env_dirs(envs_dir):
    """
    清理环境目录
    
    Args:
        envs_dir: 环境主目录路径
    
    Returns:
        int: 清理的环境数量
    """
    try:
        # 检查目录是否存在
        if not os.path.exists(envs_dir):
            logger.error(f"环境目录不存在: {envs_dir}")
            return 0
        
        # 获取所有环境目录
        env_dirs = [d for d in os.listdir(envs_dir) 
                   if os.path.isdir(os.path.join(envs_dir, d))]
        
        if not env_dirs:
            logger.info(f"没有找到环境目录: {envs_dir}")
            return 0
        
        logger.info(f"找到 {len(env_dirs)} 个环境目录")
        
        # 删除所有环境目录
        count = 0
        for env_dir in env_dirs:
            dir_path = os.path.join(envs_dir, env_dir)
            try:
                logger.info(f"删除环境目录: {dir_path}")
                shutil.rmtree(dir_path)
                count += 1
            except Exception as e:
                logger.error(f"删除环境目录时出错 {dir_path}: {str(e)}")
        
        return count
    except Exception as e:
        logger.error(f"清理环境目录时出错: {str(e)}")
        return 0

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='清理插件目录和环境')
    parser.add_argument('--data-dir', type=str, default='mcp_market_data',
                        help='数据主目录路径 (默认: mcp_market_data)')
    parser.add_argument('--plugins-only', action='store_true', 
                        help='只清理插件目录')
    parser.add_argument('--envs-only', action='store_true', 
                        help='只清理环境目录')
    
    args = parser.parse_args()
    
    # 获取数据目录的绝对路径
    data_dir = os.path.abspath(args.data_dir)
    if not os.path.exists(data_dir):
        logger.error(f"数据目录不存在: {data_dir}")
        return
    
    logger.info(f"使用数据目录: {data_dir}")
    
    plugins_dir = os.path.join(data_dir, 'plugins')
    envs_dir = os.path.join(data_dir, 'envs')
    
    # 清理插件目录
    if not args.envs_only:
        plugins_count = clean_plugin_dirs(plugins_dir)
        logger.info(f"已清理 {plugins_count} 个插件目录")
    
    # 清理环境目录
    if not args.plugins_only:
        envs_count = clean_env_dirs(envs_dir)
        logger.info(f"已清理 {envs_count} 个环境目录")
    
    logger.info("清理完成")

if __name__ == "__main__":
    main() 