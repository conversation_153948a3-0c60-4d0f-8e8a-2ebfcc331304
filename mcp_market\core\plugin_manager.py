"""
插件管理模块 - 管理MCP插件的安装、启动和卸载
"""
import os
import uuid
import logging
import json
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path

from mcp_market.models.plugin import Plugin, PluginStatus, PluginManifest, PluginSource
from mcp_market.core.repository import RepositoryManager
from mcp_market.core.dependency import DependencyManager
from mcp_market.services.process_manager import ProcessManager
from mcp_market.core.storage import create_storage

logger = logging.getLogger(__name__)


class PluginManager:
    """管理MCP插件的安装、更新、启动和卸载"""
    
    def __init__(self, data_dir: str, repo_manager: RepositoryManager, 
                 dep_manager: DependencyManager, process_manager: ProcessManager,
                 storage_type: str = "json"):
        """初始化插件管理器
        
        Args:
            data_dir: 数据目录
            repo_manager: 仓库管理器
            dep_manager: 依赖管理器
            process_manager: 进程管理器
            storage_type: 存储类型，json或sqlite
        """
        self.data_dir = Path(data_dir)
        self.repo_manager = repo_manager
        self.dep_manager = dep_manager
        self.process_manager = process_manager
        
        # 创建存储
        self.storage = create_storage(storage_type, str(self.data_dir / "storage"))
        
        # 加载插件
        self.plugins = self.storage.get_all_plugins()
        self.dependency_groups = self.storage.get_all_dependency_groups()
        
        logger.info(f"初始化插件管理器: 已加载 {len(self.plugins)} 个插件, {len(self.dependency_groups)} 个依赖组")
    
    def install_plugin(self, source_url: str, source_type: str = "git") -> Tuple[bool, Optional[str], Optional[str]]:
        """安装插件
        
        Args:
            source_url: 源URL，可以是Git仓库、本地目录或压缩包路径
            source_type: 源类型，git、local或archive
        
        Returns:
            (成功标志, 插件ID, 错误信息)
        """
        logger.info(f"安装插件: {source_url}, 类型: {source_type}")
        
        # 克隆或复制仓库
        success, local_path, error = self.repo_manager.get_repository(source_url, source_type)
        if not success:
            return False, None, f"获取仓库失败: {error}"
        
        # 获取插件清单
        manifest, error = self.repo_manager.get_manifest(local_path)
        if not manifest:
            return False, None, f"获取插件清单失败: {error}"
        
        # 创建插件ID
        plugin_id = str(uuid.uuid4())
        
        # 将字符串类型转换为枚举类型
        plugin_source = None
        if source_type == "git" or source_type == PluginSource.GIT.value:
            plugin_source = PluginSource.GIT
        elif source_type == "local" or source_type == PluginSource.LOCAL.value:
            plugin_source = PluginSource.LOCAL
        elif source_type == "archive" or source_type == PluginSource.ARCHIVE.value:
            plugin_source = PluginSource.ARCHIVE
        else:
            plugin_source = PluginSource.GIT  # 默认为GIT类型
        
        # 创建插件对象
        plugin = Plugin(
            id=plugin_id,
            name=manifest.name,
            version=manifest.version,
            description=manifest.description,
            status=PluginStatus.INSTALLING,
            source=plugin_source,
            source_url=source_url,
            local_path=local_path,
            entry_point=manifest.entry_point,
            metadata=manifest.metadata
        )
        
        # 保存插件
        self.plugins[plugin_id] = plugin
        self.storage.save_plugin(plugin)
        
        # 创建或使用依赖组
        success, group_id, error = self._setup_dependency_group(plugin, manifest)
        if not success:
            plugin.status = PluginStatus.ERROR
            plugin.error_message = f"设置依赖组失败: {error}"
            self.storage.save_plugin(plugin)
            return False, plugin_id, plugin.error_message
        
        # 安装依赖
        success, error = self._install_dependencies(plugin, manifest)
        if not success:
            plugin.status = PluginStatus.ERROR
            plugin.error_message = f"安装依赖失败: {error}"
            self.storage.save_plugin(plugin)
            return False, plugin_id, plugin.error_message
        
        # 更新插件状态
        plugin.status = PluginStatus.INSTALLED
        self.storage.save_plugin(plugin)
        
        logger.info(f"插件安装成功: {plugin_id}")
        return True, plugin_id, None
    
    def _setup_dependency_group(self, plugin: Plugin, manifest: PluginManifest) -> Tuple[bool, Optional[str], Optional[str]]:
        """设置依赖组
        
        Args:
            plugin: 插件对象
            manifest: 插件清单
        
        Returns:
            (成功标志, 依赖组ID, 错误信息)
        """
        # TODO: 实现依赖组共享逻辑
        # 暂时为每个插件创建单独的依赖组
        group_id = f"group_{plugin.id}"
        
        # 创建依赖组
        from mcp_market.models.plugin import DependencyGroup
        group = DependencyGroup(
            id=group_id,
            name=f"Group for {plugin.name}",
            python_version="3.8",  # TODO: 从manifest中获取
            base_packages=[],
            specific_packages={plugin.id: manifest.dependencies},
            env_path=""  # Initialize with empty string
        )
        
        # 创建虚拟环境
        success, env_path, error = self.dep_manager.create_environment(group)
        if not success:
            return False, None, f"创建虚拟环境失败: {error}"
        
        # 保存依赖组
        group.env_path = env_path
        self.dependency_groups[group_id] = group
        self.storage.save_dependency_group(group)
        
        # 更新插件
        plugin.dependency_group = group_id
        
        return True, group_id, None
    
    def _install_dependencies(self, plugin: Plugin, manifest: PluginManifest) -> Tuple[bool, Optional[str]]:
        """安装依赖
        
        Args:
            plugin: 插件对象
            manifest: 插件清单
        
        Returns:
            (成功标志, 错误信息)
        """
        if not plugin.dependency_group:
            return False, "插件未关联依赖组"
        
        group_id = plugin.dependency_group
        if group_id not in self.dependency_groups:
            return False, f"依赖组不存在: {group_id}"
        
        group = self.dependency_groups[group_id]
        
        # 安装插件依赖
        return self.dep_manager.install_plugin_dependencies(group, plugin.id, manifest.dependencies)
    
    def start_plugin(self, plugin_id: str) -> Tuple[bool, Optional[str], Optional[int]]:
        """启动插件
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            (成功标志, 错误信息, 端口)
        """
        logger.info(f"启动插件: {plugin_id}")
        
        if plugin_id not in self.plugins:
            return False, "插件不存在", None
        
        plugin = self.plugins[plugin_id]
        
        # 检查插件状态
        if plugin.status == PluginStatus.RUNNING:
            return True, None, None  # 已经在运行
        
        if plugin.status != PluginStatus.INSTALLED:
            return False, f"插件状态不正确: {plugin.status.value}", None
        
        # 获取入口文件路径
        entry_file = os.path.join(plugin.local_path, plugin.entry_point)
        if not os.path.exists(entry_file):
            error_msg = f"入口文件不存在: {entry_file}"
            plugin.status = PluginStatus.ERROR
            plugin.error_message = error_msg
            self.storage.save_plugin(plugin)
            return False, error_msg, None
        
        # 确保依赖组对象可用
        if not plugin.dependency_group or plugin.dependency_group not in self.dependency_groups:
            error_msg = f"插件未关联有效依赖组"
            plugin.status = PluginStatus.ERROR
            plugin.error_message = error_msg
            self.storage.save_plugin(plugin)
            return False, error_msg, None
        
        # 获取依赖组对象
        dependency_group = self.dependency_groups[plugin.dependency_group]
        
        # 启动进程
        success, port, error = self.process_manager.start_process(
            plugin=plugin,
            dependency_manager=self.dep_manager,
            entry_file=entry_file,
            dependency_group=dependency_group  # 传递依赖组对象而不是ID
        )
        
        if not success:
            plugin.status = PluginStatus.ERROR
            plugin.error_message = f"启动进程失败: {error}"
            self.storage.save_plugin(plugin)
            return False, plugin.error_message, None
        
        # 更新插件状态
        plugin.status = PluginStatus.RUNNING
        plugin.api_url = f"http://localhost:{port}"
        self.storage.save_plugin(plugin)
        
        logger.info(f"插件启动成功: {plugin_id}, 端口: {port}")
        return True, None, port
    
    def stop_plugin(self, plugin_id: str) -> Tuple[bool, Optional[str]]:
        """停止插件
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            (成功标志, 错误信息)
        """
        logger.info(f"停止插件: {plugin_id}")
        
        if plugin_id not in self.plugins:
            return False, "插件不存在"
        
        plugin = self.plugins[plugin_id]
        
        # 检查插件状态
        if plugin.status != PluginStatus.RUNNING:
            return True, None  # 不是运行状态，无需停止
        
        # 停止进程
        success, error = self.process_manager.stop_process(plugin_id)
        if not success:
            return False, f"停止进程失败: {error}"
        
        # 更新插件状态
        plugin.status = PluginStatus.INSTALLED
        plugin.api_url = None
        self.storage.save_plugin(plugin)
        
        logger.info(f"插件停止成功: {plugin_id}")
        return True, None
    
    def restart_plugin(self, plugin_id: str) -> Tuple[bool, Optional[str], Optional[int]]:
        """重启插件
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            (成功标志, 错误信息, 端口)
        """
        logger.info(f"重启插件: {plugin_id}")
        
        # 停止插件
        success, error = self.stop_plugin(plugin_id)
        if not success:
            return False, f"停止插件失败: {error}", None
        
        # 启动插件
        return self.start_plugin(plugin_id)
    
    def uninstall_plugin(self, plugin_id: str) -> Tuple[bool, Optional[str]]:
        """卸载插件
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            (成功标志, 错误信息)
        """
        logger.info(f"卸载插件: {plugin_id}")
        
        if plugin_id not in self.plugins:
            return False, "插件不存在"
        
        plugin = self.plugins[plugin_id]
        
        # 如果插件正在运行，先停止
        if plugin.status == PluginStatus.RUNNING:
            success, error = self.stop_plugin(plugin_id)
            if not success:
                return False, f"停止插件失败: {error}"
        
        # 删除依赖组
        if plugin.dependency_group and plugin.dependency_group in self.dependency_groups:
            group = self.dependency_groups[plugin.dependency_group]
            self.dep_manager.delete_dependency_group(group)
            self.storage.delete_dependency_group(plugin.dependency_group)
            del self.dependency_groups[plugin.dependency_group]
        
        # 删除本地仓库
        if plugin.local_path and os.path.exists(plugin.local_path):
            try:
                import shutil
                shutil.rmtree(plugin.local_path)
            except Exception as e:
                logger.exception(f"删除本地仓库失败: {e}")
        
        # 删除插件记录
        self.storage.delete_plugin(plugin_id)
        del self.plugins[plugin_id]
        
        logger.info(f"插件卸载成功: {plugin_id}")
        return True, None
    
    def update_plugin(self, plugin_id: str) -> Tuple[bool, Optional[str]]:
        """更新插件
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            (成功标志, 错误信息)
        """
        logger.info(f"更新插件: {plugin_id}")
        
        if plugin_id not in self.plugins:
            return False, "插件不存在"
        
        plugin = self.plugins[plugin_id]
        
        # 如果插件正在运行，先停止
        was_running = False
        if plugin.status == PluginStatus.RUNNING:
            was_running = True
            success, error = self.stop_plugin(plugin_id)
            if not success:
                return False, f"停止插件失败: {error}"
        
        # 更新仓库
        plugin.status = PluginStatus.UPDATING
        self.storage.save_plugin(plugin)
        
        success, error = self.repo_manager.update_repository(plugin.local_path, plugin.source_url, plugin.source.value)
        if not success:
            plugin.status = PluginStatus.ERROR
            plugin.error_message = f"更新仓库失败: {error}"
            self.storage.save_plugin(plugin)
            return False, plugin.error_message
        
        # 获取新的插件清单
        manifest, error = self.repo_manager.get_manifest(plugin.local_path)
        if not manifest:
            plugin.status = PluginStatus.ERROR
            plugin.error_message = f"获取插件清单失败: {error}"
            self.storage.save_plugin(plugin)
            return False, plugin.error_message
        
        # 更新插件信息
        plugin.version = manifest.version
        plugin.description = manifest.description
        plugin.entry_point = manifest.entry_point
        plugin.metadata = manifest.metadata
        
        # 更新依赖
        success, error = self._install_dependencies(plugin, manifest)
        if not success:
            plugin.status = PluginStatus.ERROR
            plugin.error_message = f"更新依赖失败: {error}"
            self.storage.save_plugin(plugin)
            return False, plugin.error_message
        
        # 更新插件状态
        plugin.status = PluginStatus.INSTALLED
        plugin.error_message = None
        self.storage.save_plugin(plugin)
        
        # 如果之前在运行，重新启动
        if was_running:
            success, error, _ = self.start_plugin(plugin_id)
            if not success:
                return False, f"重启插件失败: {error}"
        
        logger.info(f"插件更新成功: {plugin_id}")
        return True, None
    
    def get_plugin(self, plugin_id: str) -> Optional[Plugin]:
        """获取插件
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            插件对象
        """
        return self.plugins.get(plugin_id)
    
    def get_all_plugins(self) -> Dict[str, Plugin]:
        """获取所有插件
        
        Returns:
            插件ID到插件对象的映射
        """
        return self.plugins
    
    def _handle_process_exit(self, plugin_id: str, exit_code: int):
        """处理进程退出事件
        
        Args:
            plugin_id: 插件ID
            exit_code: 退出代码
        """
        if plugin_id not in self.plugins:
            return
        
        plugin = self.plugins[plugin_id]
        
        if exit_code == 0:
            # 正常退出
            plugin.status = PluginStatus.INSTALLED
        else:
            # 异常退出
            plugin.status = PluginStatus.ERROR
            plugin.error_message = f"进程异常退出，退出代码: {exit_code}"
        
        plugin.api_url = None
        self.storage.save_plugin(plugin)
        
        logger.info(f"插件进程退出: {plugin_id}, 退出代码: {exit_code}") 