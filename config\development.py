"""
Development configuration for the AI Assistant and MCP Marketplace
"""
from .default import DEFAULT_CONFIG

# Deep copy the default config to avoid modifying it
import copy
DEV_CONFIG = copy.deepcopy(DEFAULT_CONFIG)

# Override development-specific settings
DEV_CONFIG.update({
    "app": {
        "host": "127.0.0.1",  # Only listen on localhost in dev
        "port": 8081,
        "debug": True,
        "reload": True,
    }
}) 