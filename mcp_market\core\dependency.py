"""
依赖管理模块 - 创建和管理依赖组及虚拟环境
"""
import os
import sys
import subprocess
import logging
import json
import shutil
import hashlib
import platform
from typing import Dict, List, Optional, Tuple, Any, Set, Union
from pathlib import Path
import threading
import tempfile
import venv

from mcp_market.models.plugin import DependencyGroup

logger = logging.getLogger(__name__)


class DependencyManager:
    """管理Python依赖和虚拟环境"""
    
    def __init__(self, env_dir: str, cache_dir: Optional[str] = None):
        """初始化依赖管理器
        
        Args:
            env_dir: 虚拟环境目录
            cache_dir: 缓存目录，用于缓存下载的依赖包
        """
        self.env_dir = Path(env_dir)
        self.cache_dir = Path(cache_dir) if cache_dir else self.env_dir / "cache"
        
        # 创建必要的目录
        os.makedirs(self.env_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 用于同步访问的锁
        self._lock = threading.RLock()
        
        logger.info(f"初始化依赖管理器: 环境目录={self.env_dir}, 缓存目录={self.cache_dir}")
    
    def create_dependency_group(self, name: str, base_packages: List[str], 
                                specific_packages: Optional[Dict[str, List[str]]] = None,
                                python_version: str = "3.8") -> Tuple[Optional[DependencyGroup], Optional[str]]:
        """创建新的依赖组
        
        Args:
            name: 依赖组名称
            base_packages: 基础依赖包列表
            specific_packages: 特定插件的额外依赖包
            python_version: Python版本
        
        Returns:
            (依赖组对象, 错误信息)
        """
        with self._lock:
            logger.info(f"创建依赖组: {name}")
            
            # 生成唯一ID
            group_id = self._generate_group_id(name, base_packages, python_version)
            env_path = self.env_dir / group_id
            
            # 如果已存在同ID的环境，直接返回
            if env_path.exists():
                logger.info(f"依赖组已存在: {group_id}")
                return DependencyGroup(
                    id=group_id,
                    name=name,
                    base_packages=base_packages,
                    specific_packages=specific_packages or {},
                    python_version=python_version,
                    env_path=str(env_path),
                    plugins=[]
                ), None
            
            # 创建虚拟环境
            try:
                logger.info(f"创建虚拟环境: {env_path}")
                
                # 创建虚拟环境
                venv.create(env_path, with_pip=True)
                
                # 安装基础依赖
                if base_packages:
                    success, error = self._install_packages(env_path, base_packages)
                    if not success:
                        # 清理失败的环境
                        shutil.rmtree(env_path, ignore_errors=True)
                        return None, f"安装基础依赖失败: {error}"
                
                return DependencyGroup(
                    id=group_id,
                    name=name,
                    base_packages=base_packages,
                    specific_packages=specific_packages or {},
                    python_version=python_version,
                    env_path=str(env_path),
                    plugins=[]
                ), None
                
            except Exception as e:
                logger.exception(f"创建依赖组失败: {e}")
                
                # 清理失败的环境
                shutil.rmtree(env_path, ignore_errors=True)
                
                return None, str(e)
    
    def update_dependency_group(self, group: DependencyGroup) -> Tuple[bool, Optional[str]]:
        """更新依赖组
        
        Args:
            group: 依赖组对象
        
        Returns:
            (成功标志, 错误信息)
        """
        with self._lock:
            logger.info(f"更新依赖组: {group.id}")
            
            if not group.env_path or not os.path.exists(group.env_path):
                logger.error(f"依赖组环境不存在: {group.env_path}")
                return False, "依赖组环境不存在"
            
            # 更新基础依赖
            if group.base_packages:
                success, error = self._install_packages(group.env_path, group.base_packages)
                if not success:
                    return False, f"更新基础依赖失败: {error}"
            
            return True, None
    
    def install_plugin_dependencies(self, group: DependencyGroup, plugin_id: str,
                                   dependencies: List[str]) -> Tuple[bool, Optional[str]]:
        """为插件安装特定的依赖
        
        Args:
            group: 依赖组对象
            plugin_id: 插件ID
            dependencies: 依赖包列表
        
        Returns:
            (成功标志, 错误信息)
        """
        with self._lock:
            if not dependencies:
                return True, None
            
            logger.info(f"为插件 {plugin_id} 安装依赖")
            
            if not group.env_path or not os.path.exists(group.env_path):
                return False, "依赖组环境不存在"
            
            # 更新依赖组中的记录
            group.specific_packages[plugin_id] = dependencies
            
            # 安装依赖
            success, error = self._install_packages(group.env_path, dependencies)
            if not success:
                return False, f"安装插件依赖失败: {error}"
            
            return True, None
    
    def delete_dependency_group(self, group: DependencyGroup) -> bool:
        """删除依赖组
        
        Args:
            group: 依赖组对象
        
        Returns:
            是否成功
        """
        with self._lock:
            logger.info(f"删除依赖组: {group.id}")
            
            if group.env_path and os.path.exists(group.env_path):
                try:
                    shutil.rmtree(group.env_path, ignore_errors=True)
                    return True
                except Exception as e:
                    logger.exception(f"删除依赖组环境失败: {e}")
                    return False
            
            return True
    
    def find_suitable_group(self, dependencies: List[str], python_version: str = "3.8") -> Optional[DependencyGroup]:
        """查找最适合给定依赖列表的依赖组
        
        Args:
            dependencies: 依赖包列表
            python_version: Python版本
        
        Returns:
            最合适的依赖组，如果没有则返回None
        """
        # 此实现需要依赖存储模块来提供现有的依赖组
        # 在此处仅返回None，表示需要创建新的依赖组
        return None
    
    def get_python_executable(self, group: DependencyGroup) -> Optional[str]:
        """获取依赖组的Python解释器路径
        
        Args:
            group: 依赖组对象
        
        Returns:
            Python解释器路径
        """
        if not group.env_path or not os.path.exists(group.env_path):
            return None
        
        # 根据操作系统确定Python可执行文件路径
        if platform.system() == "Windows":
            python_exec = os.path.join(group.env_path, "Scripts", "python.exe")
        else:
            python_exec = os.path.join(group.env_path, "bin", "python")
        
        if os.path.exists(python_exec):
            return python_exec
        
        return None
    
    def get_installed_packages(self, group: DependencyGroup) -> List[str]:
        """获取已安装的依赖包列表
        
        Args:
            group: 依赖组对象
        
        Returns:
            已安装的依赖包列表
        """
        python_exec = self.get_python_executable(group)
        if not python_exec:
            return []
        
        try:
            result = subprocess.run(
                [python_exec, "-m", "pip", "freeze"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                logger.error(f"获取已安装包列表失败: {result.stderr}")
                return []
            
            packages = []
            for line in result.stdout.split("\n"):
                line = line.strip()
                if line and not line.startswith("#"):
                    packages.append(line)
            
            return packages
        
        except Exception as e:
            logger.exception(f"获取已安装包列表时发生异常: {e}")
            return []
    
    def _generate_group_id(self, name: str, packages: List[str], python_version: str) -> str:
        """生成依赖组ID
        
        Args:
            name: 依赖组名称
            packages: 依赖包列表
            python_version: Python版本
        
        Returns:
            依赖组ID
        """
        # 确保包列表是排序的，以保证相同内容生成相同的ID
        sorted_packages = sorted(packages)
        
        # 创建哈希输入
        hash_input = f"{name}:{python_version}:{','.join(sorted_packages)}"
        
        # 生成哈希
        hash_obj = hashlib.md5(hash_input.encode())
        hash_hex = hash_obj.hexdigest()
        
        # 创建一个包含名称和哈希的ID
        safe_name = "".join(c if c.isalnum() else "_" for c in name)
        return f"{safe_name}_{hash_hex[:8]}"
    
    def _install_packages(self, env_path: Union[str, Path], packages: List[str]) -> Tuple[bool, Optional[str]]:
        """在虚拟环境中安装依赖包
        
        Args:
            env_path: 虚拟环境路径
            packages: 依赖包列表
        
        Returns:
            (成功标志, 错误信息)
        """
        if not packages:
            return True, None
        
        env_path = Path(env_path)
        
        # 获取Python可执行文件路径
        if platform.system() == "Windows":
            python_exec = env_path / "Scripts" / "python.exe"
        else:
            python_exec = env_path / "bin" / "python"
        
        if not python_exec.exists():
            return False, f"Python解释器不存在: {python_exec}"
        
        # 构建安装命令
        cmd = [str(python_exec), "-m", "pip", "install"]
        
        # 添加缓存目录参数
        cmd.extend(["--cache-dir", str(self.cache_dir)])
        
        # 添加包列表
        cmd.extend(packages)
        
        logger.info(f"安装依赖包: {' '.join(cmd)}")
        
        try:
            # 执行安装
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                error_msg = result.stderr.strip()
                logger.error(f"安装依赖包失败: {error_msg}")
                return False, error_msg
            
            logger.info(f"依赖包安装成功")
            return True, None
        
        except Exception as e:
            logger.exception(f"安装依赖包时发生异常: {e}")
            return False, str(e)
    
    def get_dependency_group_info(self, group: DependencyGroup) -> Dict[str, Any]:
        """获取依赖组详细信息
        
        Args:
            group: 依赖组对象
        
        Returns:
            依赖组详细信息
        """
        installed_packages = self.get_installed_packages(group)
        
        python_exec = self.get_python_executable(group)
        python_version = "未知"
        
        if python_exec:
            try:
                result = subprocess.run(
                    [python_exec, "--version"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False
                )
                
                if result.returncode == 0:
                    python_version = result.stdout.strip() or result.stderr.strip()
            except Exception:
                pass
        
        return {
            "id": group.id,
            "name": group.name,
            "python_version": python_version,
            "base_packages": group.base_packages,
            "specific_packages": group.specific_packages,
            "installed_packages": installed_packages,
            "plugins_count": len(group.plugins),
            "env_path": group.env_path,
            "active": bool(python_exec and os.path.exists(python_exec))
        }
    
    def create_environment(self, group: DependencyGroup) -> Tuple[bool, Optional[str], Optional[str]]:
        """创建虚拟环境
        
        Args:
            group: 依赖组对象
        
        Returns:
            (成功标志, 环境路径, 错误信息)
        """
        with self._lock:
            logger.info(f"创建虚拟环境: {group.id}")
            
            # 确定环境路径
            env_path = os.path.join(self.env_dir, group.id)
            
            # 检查是否已存在
            if os.path.exists(env_path):
                logger.info(f"虚拟环境已存在: {env_path}")
                return True, env_path, None
            
            try:
                # 创建虚拟环境
                venv.create(env_path, with_pip=True)
                
                # 安装基础依赖
                if group.base_packages:
                    success, error = self._install_packages(env_path, group.base_packages)
                    if not success:
                        # 清理失败的环境
                        shutil.rmtree(env_path, ignore_errors=True)
                        return False, None, f"安装基础依赖失败: {error}"
                
                logger.info(f"虚拟环境创建成功: {env_path}")
                return True, env_path, None
                
            except Exception as e:
                logger.exception(f"创建虚拟环境失败: {e}")
                
                # 清理失败的环境
                if os.path.exists(env_path):
                    shutil.rmtree(env_path, ignore_errors=True)
                
                return False, None, str(e) 