"""
Core application initialization for the AI Assistant and MCP Marketplace
"""
import os
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app():
    """
    Create and configure the FastAPI application
    """
    app = FastAPI(title="AI Chat with MCP", description="AI对话网站，支持模型选择和MCP服务器配置")

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Setup templates and static files
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    templates_dir = os.path.join(base_dir, "apps", "templates")
    static_dir = os.path.join(base_dir, "apps", "static")

    templates = Jinja2Templates(directory=templates_dir)
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

    # Add middleware to handle root_path for sub-applications
    @app.middleware("http")
    async def add_root_path(request, call_next):
        """Handle path issues when mounted as a sub-application"""
        if request.scope.get("root_path"):
            request.scope["path"] = request.scope["path"].replace("/static/", "/ai-chat/static/")
        response = await call_next(request)
        return response

    return app, templates