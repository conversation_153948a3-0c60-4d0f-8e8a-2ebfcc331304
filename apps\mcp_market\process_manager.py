"""
Process manager for MCP Market application
"""
import os
import logging
import subprocess
import psutil
import json
from typing import List, Dict, Any, Optional
import asyncio
import signal

logger = logging.getLogger(__name__)

class ProcessManager:
    """Manager for MCP server processes"""
    
    def __init__(self, data_dir: str = None):
        """
        Initialize the process manager
        
        Args:
            data_dir: Directory to store process data
        """
        self.data_dir = data_dir or os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "mcp_market_data")
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        self.processes_file = os.path.join(self.data_dir, "processes.json")
        self.processes = {}
        self.process_info = self._load_processes()
    
    def _load_processes(self) -> Dict[str, Dict[str, Any]]:
        """
        Load process information from file
        
        Returns:
            Dictionary of process information
        """
        if not os.path.exists(self.processes_file):
            return {}
        
        try:
            with open(self.processes_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading processes: {e}")
            return {}
    
    def _save_processes(self):
        """Save process information to file"""
        try:
            with open(self.processes_file, "w", encoding="utf-8") as f:
                json.dump(self.process_info, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving processes: {e}")
    
    async def start_process(self, server_name: str, command: str, args: List[str] = None, 
                           env: Dict[str, str] = None, cwd: str = None) -> Dict[str, Any]:
        """
        Start a new process
        
        Args:
            server_name: Server name
            command: Command to run
            args: Command arguments
            env: Environment variables
            cwd: Working directory
            
        Returns:
            Process information
        """
        args = args or []
        env_vars = dict(os.environ)
        if env:
            env_vars.update(env)
        
        try:
            # Start the process
            process = subprocess.Popen(
                [command] + args,
                env=env_vars,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )
            
            # Store process information
            pid = process.pid
            self.processes[server_name] = process
            self.process_info[server_name] = {
                "pid": pid,
                "command": command,
                "args": args,
                "env": env,
                "cwd": cwd,
                "status": "running"
            }
            self._save_processes()
            
            return self.process_info[server_name]
        except Exception as e:
            logger.error(f"Error starting process for {server_name}: {e}")
            self.process_info[server_name] = {
                "command": command,
                "args": args,
                "env": env,
                "cwd": cwd,
                "status": "error",
                "error": str(e)
            }
            self._save_processes()
            return self.process_info[server_name]
    
    async def stop_process(self, server_name: str) -> bool:
        """
        Stop a process
        
        Args:
            server_name: Server name
            
        Returns:
            True if stopped, False otherwise
        """
        if server_name not in self.processes:
            return False
        
        process = self.processes[server_name]
        try:
            # Try to terminate gracefully
            process.terminate()
            
            # Wait for process to terminate
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                # Force kill if it doesn't terminate
                process.kill()
            
            # Update process information
            if server_name in self.process_info:
                self.process_info[server_name]["status"] = "stopped"
                self._save_processes()
            
            # Remove from active processes
            del self.processes[server_name]
            
            return True
        except Exception as e:
            logger.error(f"Error stopping process for {server_name}: {e}")
            return False
    
    def get_process_status(self, server_name: str) -> Dict[str, Any]:
        """
        Get process status
        
        Args:
            server_name: Server name
            
        Returns:
            Process status information
        """
        if server_name not in self.process_info:
            return {"status": "not_found"}
        
        info = self.process_info[server_name].copy()
        
        # Check if process is still running
        if server_name in self.processes:
            process = self.processes[server_name]
            if process.poll() is None:
                info["status"] = "running"
            else:
                info["status"] = "stopped"
                info["exit_code"] = process.returncode
        
        return info
    
    def get_all_processes(self) -> Dict[str, Dict[str, Any]]:
        """
        Get status of all processes
        
        Returns:
            Dictionary of process status information
        """
        result = {}
        for server_name in self.process_info:
            result[server_name] = self.get_process_status(server_name)
        return result 