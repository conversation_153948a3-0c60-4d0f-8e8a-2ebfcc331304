"""
Repository manager for MCP Market application
"""
import os
import logging
import json
import shutil
from typing import List, Dict, Any, Optional
import git

logger = logging.getLogger(__name__)

class RepositoryManager:
    """Manager for MCP plugin repositories"""
    
    def __init__(self, repos_dir: str = None):
        """
        Initialize the repository manager
        
        Args:
            repos_dir: Directory to store repositories
        """
        self.repos_dir = repos_dir or os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "mcp_market_data", "repos")
        if not os.path.exists(self.repos_dir):
            os.makedirs(self.repos_dir)
        
        self.repos_file = os.path.join(os.path.dirname(self.repos_dir), "repositories.json")
        self.repositories = self._load_repositories()
    
    def _load_repositories(self) -> Dict[str, Dict[str, Any]]:
        """
        Load repository information from file
        
        Returns:
            Dictionary of repository information
        """
        if not os.path.exists(self.repos_file):
            return {}
        
        try:
            with open(self.repos_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading repositories: {e}")
            return {}
    
    def _save_repositories(self):
        """Save repository information to file"""
        try:
            with open(self.repos_file, "w", encoding="utf-8") as f:
                json.dump(self.repositories, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving repositories: {e}")
    
    def clone_repository(self, name: str, url: str) -> Dict[str, Any]:
        """
        Clone a repository
        
        Args:
            name: Repository name
            url: Repository URL
            
        Returns:
            Repository information
        """
        repo_path = os.path.join(self.repos_dir, name)
        
        # Remove if exists
        if os.path.exists(repo_path):
            shutil.rmtree(repo_path)
        
        try:
            # Clone the repository
            repo = git.Repo.clone_from(url, repo_path)
            
            # Store repository information
            self.repositories[name] = {
                "name": name,
                "url": url,
                "path": repo_path,
                "status": "cloned",
                "last_commit": str(repo.head.commit),
                "branch": repo.active_branch.name
            }
            self._save_repositories()
            
            return self.repositories[name]
        except Exception as e:
            logger.error(f"Error cloning repository {name} from {url}: {e}")
            self.repositories[name] = {
                "name": name,
                "url": url,
                "path": repo_path,
                "status": "error",
                "error": str(e)
            }
            self._save_repositories()
            return self.repositories[name]
    
    def update_repository(self, name: str) -> Dict[str, Any]:
        """
        Update a repository
        
        Args:
            name: Repository name
            
        Returns:
            Repository information
        """
        if name not in self.repositories:
            return {"status": "not_found", "name": name}
        
        repo_info = self.repositories[name]
        repo_path = repo_info["path"]
        
        if not os.path.exists(repo_path):
            return self.clone_repository(name, repo_info["url"])
        
        try:
            # Open the repository
            repo = git.Repo(repo_path)
            
            # Pull changes
            origin = repo.remotes.origin
            origin.pull()
            
            # Update repository information
            repo_info["status"] = "updated"
            repo_info["last_commit"] = str(repo.head.commit)
            repo_info["branch"] = repo.active_branch.name
            self._save_repositories()
            
            return repo_info
        except Exception as e:
            logger.error(f"Error updating repository {name}: {e}")
            repo_info["status"] = "error"
            repo_info["error"] = str(e)
            self._save_repositories()
            return repo_info
    
    def get_repository(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get repository information
        
        Args:
            name: Repository name
            
        Returns:
            Repository information if found, None otherwise
        """
        return self.repositories.get(name)
    
    def get_all_repositories(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information for all repositories
        
        Returns:
            Dictionary of repository information
        """
        return self.repositories
    
    def delete_repository(self, name: str) -> bool:
        """
        Delete a repository
        
        Args:
            name: Repository name
            
        Returns:
            True if deleted, False otherwise
        """
        if name not in self.repositories:
            return False
        
        repo_path = self.repositories[name]["path"]
        
        try:
            # Remove the repository directory
            if os.path.exists(repo_path):
                shutil.rmtree(repo_path)
            
            # Remove from repository information
            del self.repositories[name]
            self._save_repositories()
            
            return True
        except Exception as e:
            logger.error(f"Error deleting repository {name}: {e}")
            return False 