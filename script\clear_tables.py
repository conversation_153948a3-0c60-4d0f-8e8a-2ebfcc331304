#!/usr/bin/env python
"""
清空数据库表数据的脚本
"""
import os
import sqlite3
import logging
import argparse
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clear_table(db_path, table_name):
    """
    清空指定表的所有数据
    
    Args:
        db_path: 数据库文件路径
        table_name: 表名
    
    Returns:
        bool: 操作是否成功
    """
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取表中的记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count_before = cursor.fetchone()[0]
        
        # 删除表中的所有数据
        cursor.execute(f"DELETE FROM {table_name}")
        
        # 提交事务
        conn.commit()
        
        # 验证删除结果
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count_after = cursor.fetchone()[0]
        
        # 关闭连接
        conn.close()
        
        logger.info(f"表 {table_name} 已清空: 删除前 {count_before} 条记录，删除后 {count_after} 条记录")
        return True
    except Exception as e:
        logger.error(f"清空表 {table_name} 时出错: {str(e)}")
        return False

def list_tables(db_path):
    """
    列出数据库中的所有表
    
    Args:
        db_path: 数据库文件路径
    
    Returns:
        list: 表名列表
    """
    try:
        # 连接数据库
        logger.info(f"尝试连接数据库: {db_path}")
        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            return []
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # 关闭连接
        conn.close()
        
        if not tables:
            logger.info("数据库中没有找到任何表")
        
        return tables
    except Exception as e:
        logger.error(f"获取表列表时出错: {str(e)}")
        return []

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='清空数据库表数据')
    parser.add_argument('--db-path', type=str, default='mcp_market_data/storage/mcp_market.db',
                        help='数据库文件路径 (默认: mcp_market_data/storage/mcp_market.db)')
    parser.add_argument('--table', type=str, help='要清空的表名，不指定则清空所有表')
    parser.add_argument('--list', action='store_true', help='列出所有表名')
    
    args = parser.parse_args()
    
    # 获取数据库文件的绝对路径
    db_path = os.path.abspath(args.db_path)
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return
    
    logger.info(f"使用数据库: {db_path}")
    
    # 列出所有表
    if args.list:
        tables = list_tables(db_path)
        if tables:
            logger.info(f"数据库中的表: {', '.join(tables)}")
        else:
            logger.info("数据库中没有表或获取表列表失败")
        return
    
    # 如果指定了表名，只清空该表
    if args.table:
        clear_table(db_path, args.table)
        return
    
    # 获取所有表名并清空
    tables = list_tables(db_path)
    if not tables:
        logger.info("没有找到表或获取表列表失败")
        return
    
    logger.info(f"准备清空以下表: {', '.join(tables)}")
    for table in tables:
        clear_table(db_path, table)
    
    logger.info("所有表清空完成")

if __name__ == "__main__":
    main() 