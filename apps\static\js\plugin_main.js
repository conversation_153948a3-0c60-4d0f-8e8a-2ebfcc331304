/**
 * MCP插件市场前端JavaScript
 */

document.addEventListener('DOMContentLoaded', () => {
    const app = Vue.createApp({
        data() {
            return {
                // 页面状态
                activeTab: 'plugins',
                currentLogType: 'stdout',
                logContent: '',
                currentPluginId: null,
                toastMessage: '',

                // 数据
                availablePlugins: [],
                installedPlugins: [],
                runningPlugins: [],
                dependencyGroups: [],

                // 新插件表单
                newPlugin: {
                    repo_url: '',
                    branch: ''
                },

                // 新依赖组表单
                newDependency: {
                    name: '',
                    python_version: '3.9'
                },
                basePackagesText: ''
            };
        },
        mounted() {
            // 初始化Bootstrap组件
            this.bootstrapInit();
            
            // 加载数据
            this.loadPlugins();
            this.loadDependencies();
            
            // 设置轮询
            setInterval(() => {
                this.loadRunningPlugins();
            }, 10000); // 每10秒更新一次运行状态
        },
        methods: {
            // UI操作
            setActiveTab(tab) {
                this.activeTab = tab;
                
                // 当切换到特定标签时刷新数据
                if (tab === 'plugins') {
                    this.loadPlugins();
                } else if (tab === 'installed') {
                    this.loadInstalledPlugins();
                } else if (tab === 'running') {
                    this.loadRunningPlugins();
                } else if (tab === 'dependencies') {
                    this.loadDependencies();
                }
            },
            
            showAddPluginModal() {
                const modal = new bootstrap.Modal(document.getElementById('addPluginModal'));
                modal.show();
            },
            
            showCreateDependencyModal() {
                const modal = new bootstrap.Modal(document.getElementById('createDependencyModal'));
                modal.show();
            },
            
            showToast(message) {
                this.toastMessage = message;
                const toastEl = document.getElementById('toast');
                const toast = new bootstrap.Toast(toastEl);
                toast.show();
            },
            
            getStatusClass(status) {
                switch (status) {
                    case 'available':
                        return 'bg-primary';
                    case 'installed':
                        return 'bg-success';
                    case 'running':
                        return 'bg-info';
                    case 'updating':
                        return 'bg-warning';
                    case 'error':
                        return 'bg-danger';
                    default:
                        return 'bg-secondary';
                }
            },
            
            // Bootstrap初始化
            bootstrapInit() {
                // 获取所有模态框元素并初始化
                document.querySelectorAll('.modal').forEach(modalEl => {
                    new bootstrap.Modal(modalEl);
                });
            },
            
            // 数据加载
            async loadPlugins() {
                try {
                    const response = await axios.get('/api/plugins/available');
                    this.availablePlugins = response.data.data || [];
                } catch (error) {
                    console.error('加载可用插件失败:', error);
                    this.showToast('加载可用插件失败: ' + (error.response?.data?.message || error.message));
                }
                
                this.loadInstalledPlugins();
            },
            
            async loadInstalledPlugins() {
                try {
                    const response = await axios.get('/api/plugins/installed');
                    this.installedPlugins = response.data.data || [];
                } catch (error) {
                    console.error('加载已安装插件失败:', error);
                    this.showToast('加载已安装插件失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            async loadRunningPlugins() {
                try {
                    const response = await axios.get('/api/plugins/running');
                    this.runningPlugins = response.data.data || [];
                } catch (error) {
                    console.error('加载运行中插件失败:', error);
                    this.showToast('加载运行中插件失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            async loadDependencies() {
                try {
                    const response = await axios.get('/api/dependencies');
                    this.dependencyGroups = response.data.data || [];
                } catch (error) {
                    console.error('加载依赖组失败:', error);
                    this.showToast('加载依赖组失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            // 插件操作
            async addPlugin() {
                if (!this.newPlugin.repo_url) {
                    this.showToast('请输入仓库URL');
                    return;
                }
                
                try {
                    const response = await axios.post('/api/plugins/add', {
                        repo_url: this.newPlugin.repo_url,
                        branch: this.newPlugin.branch || undefined
                    });
                    
                    if (response.data.success) {
                        this.showToast('插件添加成功');
                        this.newPlugin.repo_url = '';
                        this.newPlugin.branch = '';
                        
                        // 关闭模态框
                        const modalEl = document.getElementById('addPluginModal');
                        const modal = bootstrap.Modal.getInstance(modalEl);
                        modal.hide();
                        
                        // 刷新插件列表
                        this.loadPlugins();
                    } else {
                        this.showToast('插件添加失败: ' + response.data.message);
                    }
                } catch (error) {
                    console.error('添加插件失败:', error);
                    this.showToast('添加插件失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            async installPlugin(pluginId) {
                try {
                    const response = await axios.post(`/api/plugins/${pluginId}/install`);
                    
                    if (response.data.success) {
                        this.showToast('插件安装成功');
                        this.loadPlugins();
                    } else {
                        this.showToast('插件安装失败: ' + response.data.message);
                    }
                } catch (error) {
                    console.error('安装插件失败:', error);
                    this.showToast('安装插件失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            async updatePlugin(pluginId) {
                try {
                    const response = await axios.post(`/api/plugins/${pluginId}/update`);
                    
                    if (response.data.success) {
                        this.showToast('插件更新成功');
                        this.loadInstalledPlugins();
                    } else {
                        this.showToast('插件更新失败: ' + response.data.message);
                    }
                } catch (error) {
                    console.error('更新插件失败:', error);
                    this.showToast('更新插件失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            async uninstallPlugin(pluginId) {
                if (!confirm('确定要卸载此插件吗?')) return;
                
                try {
                    const response = await axios.post(`/api/plugins/${pluginId}/uninstall`);
                    
                    if (response.data.success) {
                        this.showToast('插件卸载成功');
                        this.loadPlugins();
                    } else {
                        this.showToast('插件卸载失败: ' + response.data.message);
                    }
                } catch (error) {
                    console.error('卸载插件失败:', error);
                    this.showToast('卸载插件失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            async startPlugin(pluginId) {
                try {
                    const response = await axios.post(`/api/plugins/${pluginId}/start`);
                    
                    if (response.data.success) {
                        this.showToast('插件启动成功');
                        setTimeout(() => {
                            this.loadInstalledPlugins();
                            this.loadRunningPlugins();
                        }, 1000);
                    } else {
                        this.showToast('插件启动失败: ' + response.data.message);
                    }
                } catch (error) {
                    console.error('启动插件失败:', error);
                    this.showToast('启动插件失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            async stopPlugin(pluginId) {
                try {
                    const response = await axios.post(`/api/plugins/${pluginId}/stop`);
                    
                    if (response.data.success) {
                        this.showToast('插件停止成功');
                        setTimeout(() => {
                            this.loadInstalledPlugins();
                            this.loadRunningPlugins();
                        }, 1000);
                    } else {
                        this.showToast('插件停止失败: ' + response.data.message);
                    }
                } catch (error) {
                    console.error('停止插件失败:', error);
                    this.showToast('停止插件失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            async restartPlugin(pluginId) {
                try {
                    const response = await axios.post(`/api/plugins/${pluginId}/restart`);
                    
                    if (response.data.success) {
                        this.showToast('插件重启成功');
                        setTimeout(() => {
                            this.loadInstalledPlugins();
                            this.loadRunningPlugins();
                        }, 1000);
                    } else {
                        this.showToast('插件重启失败: ' + response.data.message);
                    }
                } catch (error) {
                    console.error('重启插件失败:', error);
                    this.showToast('重启插件失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            // 日志查看
            async showLogs(pluginId) {
                this.currentPluginId = pluginId;
                this.refreshLogs();
                
                const modal = new bootstrap.Modal(document.getElementById('logsModal'));
                modal.show();
            },
            
            async refreshLogs() {
                if (!this.currentPluginId) return;
                
                try {
                    const response = await axios.get(`/api/plugins/${this.currentPluginId}/logs?type=${this.currentLogType}`);
                    
                    if (response.data.success) {
                        this.logContent = response.data.data || '无日志内容';
                    } else {
                        this.logContent = '获取日志失败: ' + response.data.message;
                    }
                } catch (error) {
                    console.error('获取日志失败:', error);
                    this.logContent = '获取日志失败: ' + (error.response?.data?.message || error.message);
                }
            },
            
            // 依赖管理
            async createDependencyGroup() {
                if (!this.newDependency.name) {
                    this.showToast('请输入依赖组名称');
                    return;
                }
                
                // 解析依赖包文本
                const basePackages = this.basePackagesText
                    .split('\n')
                    .map(line => line.trim())
                    .filter(line => line && !line.startsWith('#'));
                
                try {
                    const response = await axios.post('/api/dependencies', {
                        name: this.newDependency.name,
                        python_version: this.newDependency.python_version,
                        base_packages: basePackages
                    });
                    
                    if (response.data.success) {
                        this.showToast('依赖组创建成功');
                        this.newDependency.name = '';
                        this.basePackagesText = '';
                        
                        // 关闭模态框
                        const modalEl = document.getElementById('createDependencyModal');
                        const modal = bootstrap.Modal.getInstance(modalEl);
                        modal.hide();
                        
                        // 刷新依赖组列表
                        this.loadDependencies();
                    } else {
                        this.showToast('依赖组创建失败: ' + response.data.message);
                    }
                } catch (error) {
                    console.error('创建依赖组失败:', error);
                    this.showToast('创建依赖组失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            async viewDependencyDetail(groupId) {
                try {
                    const response = await axios.get(`/api/dependencies/${groupId}`);
                    
                    if (response.data.success) {
                        const group = response.data.data;
                        let message = `<strong>${group.name}</strong> (Python ${group.python_version})<br><br>`;
                        message += `<strong>基础包:</strong><br>`;
                        
                        if (group.base_packages && group.base_packages.length) {
                            message += `<code>${group.base_packages.join('<br>')}</code><br><br>`;
                        } else {
                            message += `无<br><br>`;
                        }
                        
                        message += `<strong>使用此依赖组的插件:</strong><br>`;
                        if (group.plugins && group.plugins.length) {
                            message += group.plugins.map(p => p.name || p.id).join('<br>');
                        } else {
                            message += '无插件使用此依赖组';
                        }
                        
                        this.showToast(message);
                    } else {
                        this.showToast('获取依赖组详情失败: ' + response.data.message);
                    }
                } catch (error) {
                    console.error('查看依赖组详情失败:', error);
                    this.showToast('查看依赖组详情失败: ' + (error.response?.data?.message || error.message));
                }
            },
            
            async deleteDependencyGroup(groupId) {
                if (!confirm('确定要删除此依赖组吗?')) return;
                
                try {
                    const response = await axios.delete(`/api/dependencies/${groupId}`);
                    
                    if (response.data.success) {
                        this.showToast('依赖组删除成功');
                        this.loadDependencies();
                    } else {
                        this.showToast('依赖组删除失败: ' + response.data.message);
                    }
                } catch (error) {
                    console.error('删除依赖组失败:', error);
                    this.showToast('删除依赖组失败: ' + (error.response?.data?.message || error.message));
                }
            }
        }
    });
    
    // 挂载Vue应用
    app.mount('#app');
}); 