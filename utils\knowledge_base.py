"""
Knowledge Base Management utilities
"""
from pathlib import Path
import os
from typing import List, Dict, Optional
from pydantic import BaseModel
from datetime import datetime

class DocumentInfo(BaseModel):
    """文档信息"""
    name: str
    path: str
    size: int
    modified: float
    chunks: int = 0
    status: str = "processed"

class SearchResult(BaseModel):
    """搜索结果"""
    content: str
    source: str
    similarity: float

class KnowledgeBaseManager:
    """知识库管理器"""
    
    def __init__(self, 
                 documents_dir: str = "kb_documents",
                 vectorstore_dir: str = "vectorstore_db",
                 embedding_model_name: str = "sentence-transformers/all-MiniLM-L6-v2"):
        self.documents_dir = Path(documents_dir)
        self.vectorstore_dir = Path(vectorstore_dir)
        self.embedding_model_name = embedding_model_name
        self.embeddings = None
        self.vector_store = None
        
        # 确保必要的目录存在
        self.documents_dir.mkdir(exist_ok=True)
        self.vectorstore_dir.mkdir(exist_ok=True)
        
        # 初始化组件
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化必要的组件"""
        try:
            # 尝试导入和初始化嵌入模型
            self._initialize_embeddings()
            # 初始化向量存储
            self._initialize_vector_store()
        except ImportError as e:
            print(f"Warning: Some dependencies not available: {e}")
            print("Knowledge base functionality may be limited")
    
    def _initialize_embeddings(self):
        """初始化嵌入模型"""
        try:
            from langchain_huggingface import HuggingFaceEmbeddings
            import torch
            
            # 检查是否有GPU可用
            device = "cuda" if torch.cuda.is_available() else "cpu"
            
            self.embeddings = HuggingFaceEmbeddings(
                model_name=self.embedding_model_name,
                model_kwargs={'device': device},
                encode_kwargs={'normalize_embeddings': True}
            )
            print(f"Initialized embeddings model: {self.embedding_model_name} on {device}")
        except ImportError:
            print("HuggingFace embeddings not available")
            self.embeddings = None
    
    def _initialize_vector_store(self):
        """初始化向量存储"""
        try:
            from langchain_community.vectorstores import Chroma
            
            if self.embeddings:
                # 尝试加载现有的向量存储
                if (self.vectorstore_dir / "chroma.sqlite3").exists():
                    self.vector_store = Chroma(
                        persist_directory=str(self.vectorstore_dir),
                        embedding_function=self.embeddings
                    )
                    print(f"Loaded existing vector store from {self.vectorstore_dir}")
                else:
                    # 创建新的向量存储
                    self.vector_store = Chroma(
                        persist_directory=str(self.vectorstore_dir),
                        embedding_function=self.embeddings
                    )
                    print(f"Created new vector store at {self.vectorstore_dir}")
        except ImportError:
            print("Chroma vector store not available")
            self.vector_store = None
    
    def get_document_info(self) -> List[DocumentInfo]:
        """获取文档目录中所有文档的信息"""
        documents = []
        
        for file_path in self.documents_dir.rglob("*"):
            if file_path.is_file():
                stat = file_path.stat()
                doc_info = DocumentInfo(
                    name=file_path.name,
                    path=str(file_path.relative_to(self.documents_dir)),
                    size=stat.st_size,
                    modified=stat.st_mtime
                )
                documents.append(doc_info)
        
        return documents
    
    def load_documents(self) -> List[DocumentInfo]:
        """加载并处理文档目录中的所有文档"""
        try:
            from langchain_community.document_loaders import DirectoryLoader, UnstructuredFileLoader
            from langchain.text_splitter import RecursiveCharacterTextSplitter
            
            # 初始化文本分割器
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200,
                length_function=len,
            )
            
            # 使用DirectoryLoader加载文档
            loader = DirectoryLoader(
                str(self.documents_dir),
                glob="**/*.*",  # 匹配所有文件
                loader_cls=UnstructuredFileLoader,
                show_progress=True,
                use_multithreading=True
            )
            documents = loader.load()
            
            # 分割文档
            chunks = []
            for doc in documents:
                doc_chunks = text_splitter.split_documents([doc])
                chunks.extend(doc_chunks)
            
            # 将分割后的文档添加到向量存储
            if chunks and self.vector_store:
                self.vector_store.add_documents(chunks)
                # 持久化向量存储
                self.vector_store.persist()
                print(f"Processed {len(documents)} documents into {len(chunks)} chunks")
            
            # 返回文档信息
            return self.get_document_info()
            
        except ImportError as e:
            print(f"Document loading dependencies not available: {e}")
            return self.get_document_info()
        except Exception as e:
            print(f"Error loading documents: {str(e)}")
            return []
    
    def search(self, query: str, k: int = 5) -> List[SearchResult]:
        """搜索相关文档"""
        if not self.vector_store:
            print("Vector store not available")
            return []
        
        try:
            # 执行相似性搜索
            results = self.vector_store.similarity_search_with_score(query, k=k)
            
            search_results = []
            for doc, score in results:
                result = SearchResult(
                    content=doc.page_content,
                    source=doc.metadata.get('source', 'Unknown'),
                    similarity=float(1 - score)  # 转换为相似度分数
                )
                search_results.append(result)
            
            return search_results
        except Exception as e:
            print(f"Error searching documents: {str(e)}")
            return []
    
    def get_relevant_context(self, query: str, max_tokens: int = 2000) -> str:
        """获取与查询相关的上下文"""
        results = self.search(query)
        context = "\n\n".join([
            f"From {result.source}:\n{result.content}"
            for result in results
        ])
        return context[:max_tokens]  # 简单截断，实际应用中可能需要更智能的方式
    
    def delete_document(self, filename: str) -> bool:
        """删除文档及其向量"""
        try:
            file_path = self.documents_dir / filename
            if file_path.exists():
                # 删除文件
                file_path.unlink()
                
                # 重新初始化向量存储（简单方案，实际应用中可能需要更精细的控制）
                if self.vector_store:
                    self.vector_store = None
                    self._initialize_vector_store()
                
                return True
            return False
        except Exception as e:
            print(f"Error deleting document: {str(e)}")
            return False
    
    def add_document_from_text(self, filename: str, content: str) -> bool:
        """从文本内容添加文档"""
        try:
            file_path = self.documents_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 重新加载文档以更新向量存储
            self.load_documents()
            return True
        except Exception as e:
            print(f"Error adding document: {str(e)}")
            return False
