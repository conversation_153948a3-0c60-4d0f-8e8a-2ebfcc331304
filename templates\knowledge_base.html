{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h2>知识库管理</h2>
    
    <!-- 文档上传 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">上传文档</h5>
        </div>
        <div class="card-body">
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="document" class="form-label">选择文件 (支持PDF, TXT, DOCX等)</label>
                    <input type="file" class="form-control" id="document" name="file" accept=".pdf,.txt,.docx,.md">
                </div>
                <button type="submit" class="btn btn-primary">上传</button>
            </form>
        </div>
    </div>

    <!-- 文档列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">已上传文档</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>大小</th>
                            <th>修改时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="documentsList">
                        <!-- 文档列表将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// 加载文档列表
function loadDocuments() {
    fetch('/api/knowledge-base/documents')
        .then(response => response.json())
        .then(documents => {
            const tbody = document.getElementById('documentsList');
            tbody.innerHTML = '';
            documents.forEach(doc => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${doc.filename}</td>
                    <td>${formatFileSize(doc.size)}</td>
                    <td>${new Date(doc.modified * 1000).toLocaleString()}</td>
                    <td>
                        <button class="btn btn-danger btn-sm" onclick="deleteDocument('${doc.filename}')">
                            删除
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        })
        .catch(error => console.error('Error loading documents:', error));
}

// 文件上传处理
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData();
    const fileInput = document.getElementById('document');
    formData.append('file', fileInput.files[0]);

    fetch('/api/knowledge-base/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        alert(data.message);
        loadDocuments();
        fileInput.value = ''; // 清空文件输入
    })
    .catch(error => {
        console.error('Error uploading file:', error);
        alert('上传失败，请重试');
    });
});

// 删除文档
function deleteDocument(filename) {
    if (confirm(`确定要删除文档 ${filename} 吗？`)) {
        fetch(`/api/knowledge-base/documents/${filename}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            loadDocuments();
        })
        .catch(error => {
            console.error('Error deleting document:', error);
            alert('删除失败，请重试');
        });
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 页面加载时获取文档列表
document.addEventListener('DOMContentLoaded', loadDocuments);
</script>
{% endblock %} 