#!/usr/bin/env python
"""
重置 MCP Market 应用的脚本

此脚本会：
1. 清空数据库表中的所有数据
2. 删除所有插件目录
3. 删除所有环境目录

使用前请确保 MCP Market 应用已停止运行
"""
import os
import sys
import subprocess
import logging
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_script(script_path, args=None):
    """
    运行指定的脚本
    
    Args:
        script_path: 脚本文件路径
        args: 脚本参数列表
    
    Returns:
        bool: 操作是否成功
    """
    cmd = [sys.executable, script_path]
    if args:
        cmd.extend(args)
    
    try:
        logger.info(f"运行脚本: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"脚本运行失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"运行脚本时出错: {str(e)}")
        return False

def check_mcp_market_running():
    """
    检查 MCP Market 是否正在运行
    
    Returns:
        bool: 是否正在运行
    """
    # 这里只是一个简单的检查，实际上可能需要更复杂的逻辑
    try:
        # 检查是否有python进程包含mcp_market
        if sys.platform == 'win32':
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            if 'mcp_market' in result.stdout:
                return True
        else:
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            if 'mcp_market' in result.stdout and 'python' in result.stdout:
                return True
        return False
    except Exception as e:
        logger.error(f"检查MCP Market运行状态时出错: {str(e)}")
        # 如果检查出错，假设它可能在运行，以避免风险
        return True

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='重置 MCP Market 应用')
    parser.add_argument('--data-dir', type=str, default='mcp_market_data',
                        help='数据主目录路径 (默认: mcp_market_data)')
    parser.add_argument('--force', action='store_true',
                        help='强制重置，即使检测到MCP Market可能正在运行')
    parser.add_argument('--keep-db', action='store_true',
                        help='保留数据库数据，只清理插件和环境目录')
    
    args = parser.parse_args()
    
    # 获取数据目录的绝对路径
    data_dir = os.path.abspath(args.data_dir)
    if not os.path.exists(data_dir):
        logger.error(f"数据目录不存在: {data_dir}")
        return
    
    logger.info(f"使用数据目录: {data_dir}")
    
    # 检查MCP Market是否在运行
    if check_mcp_market_running() and not args.force:
        logger.error("检测到MCP Market可能正在运行。请先停止MCP Market，或使用--force参数强制重置。")
        return
    
    # 获取脚本文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    clear_tables_script = os.path.join(current_dir, 'clear_tables.py')
    clean_plugins_script = os.path.join(current_dir, 'clean_plugins.py')
    
    # 清空数据库表
    if not args.keep_db:
        db_path = os.path.join(data_dir, 'storage', 'mcp_market.db')
        if os.path.exists(db_path):
            success = run_script(clear_tables_script, ['--db-path', db_path])
            if not success:
                logger.warning("清空数据库表失败，继续执行后续步骤")
        else:
            logger.warning(f"数据库文件不存在: {db_path}")
    
    # 清理插件和环境目录
    success = run_script(clean_plugins_script, ['--data-dir', data_dir])
    if not success:
        logger.warning("清理插件和环境目录失败")
    
    logger.info("MCP Market 重置完成")

if __name__ == "__main__":
    main() 