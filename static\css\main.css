/**
 * MCP插件市场样式
 */

/* Main styles for MCP Plugin Marketplace */

/* Base styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background-color: #f8f9fa;
}

/* Sidebar styles */
.sidebar {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

/* Card styles */
.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-title {
    font-weight: 600;
}

.card-footer {
    background-color: transparent;
}

/* Badge styles */
.badge {
    font-weight: 500;
    padding: 0.5em 0.8em;
}

/* Button styles */
.btn {
    font-weight: 500;
}

.btn-primary {
    background-color: #0d6efd;
}

.btn-success {
    background-color: #198754;
}

.btn-danger {
    background-color: #dc3545;
}

.btn-info {
    background-color: #0dcaf0;
    color: #000;
}

.btn-warning {
    background-color: #ffc107;
    color: #000;
}

/* Toast styles */
.toast-container {
    z-index: 1100;
}

.toast {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Modal styles */
.modal-content {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Logs pre styles */
pre.bg-dark {
    border-radius: 0.25rem;
}

/* Animation for status changes */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

.bg-warning {
    animation: pulse 2s infinite;
}

/* Table styles */
.table {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border-radius: 0.25rem;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* Main content area */
.main-content {
    padding: 1rem;
    overflow-y: auto;
}

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 侧边栏样式 */
.sidebar {
    height: 100vh;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    color: #495057;
    border-radius: 5px;
    margin-bottom: 5px;
    transition: all 0.2s;
}

.sidebar .nav-link:hover {
    background-color: #e9ecef;
}

.sidebar .nav-link.active {
    background-color: #6366f1;
    color: white;
}

/* 主内容区 */
.main-content {
    height: 100vh;
    overflow-y: auto;
    background-color: #f8f9fa;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
    font-weight: 600;
    color: #495057;
}

.card-footer {
    background-color: #fff;
    border-top: 1px solid #f1f1f1;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.4em 0.6em;
}

/* 按钮样式 */
.btn {
    font-weight: 500;
    border-radius: 5px;
}

.btn-primary {
    background-color: #6366f1;
    border-color: #6366f1;
}

.btn-primary:hover {
    background-color: #4f46e5;
    border-color: #4f46e5;
}

.btn-success {
    background-color: #10b981;
    border-color: #10b981;
}

.btn-success:hover {
    background-color: #059669;
    border-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    border-color: #ef4444;
}

.btn-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}

.btn-info {
    background-color: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.btn-info:hover {
    background-color: #2563eb;
    border-color: #2563eb;
    color: white;
}

.btn-warning {
    background-color: #f59e0b;
    border-color: #f59e0b;
    color: white;
}

.btn-warning:hover {
    background-color: #d97706;
    border-color: #d97706;
    color: white;
}

/* 表单样式 */
.form-control:focus, .form-select:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
}

/* 状态徽章 */
.status-badge {
    font-size: 0.8rem;
    padding: 0.35em 0.65em;
}

/* 预格式化文本 */
pre {
    background-color: #282c34;
    color: #abb2bf;
    border-radius: 6px;
    padding: 15px;
}

/* Toast样式 */
.toast {
    z-index: 1100;
    min-width: 300px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: auto;
        z-index: 1000;
    }
    
    .main-content {
        margin-top: 60px;
        height: calc(100vh - 60px);
    }
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter-from, .fade-leave-to {
    opacity: 0;
}

/* 日志查看器 */
.log-viewer {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    padding: 15px;
    border-radius: 5px;
    height: 400px;
    overflow-y: auto;
} 