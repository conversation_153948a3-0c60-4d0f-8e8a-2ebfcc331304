from pathlib import Path
import os
from typing import List, Dict, Optional
from pydantic import BaseModel
from langchain_community.document_loaders import DirectoryLoader, UnstructuredFileLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import Chroma
from datetime import datetime
import torch

class DocumentInfo(BaseModel):
    """文档信息"""
    name: str
    path: str
    size: int
    modified: float
    chunks: int = 0
    status: str = "processed"

class SearchResult(BaseModel):
    """搜索结果"""
    content: str
    source: str
    similarity: float

class KnowledgeBaseManager:
    def __init__(self, 
                 documents_dir: str = "kb_documents",
                 vectorstore_dir: str = "vectorstore_db",
                 embedding_model_name: str = "sentence-transformers/all-MiniLM-L6-v2"):
        self.documents_dir = Path(documents_dir)
        self.vectorstore_dir = Path(vectorstore_dir)
        self.embedding_model_name = embedding_model_name
        self.embeddings = None
        self.vector_store = None
        
        # 确保必要的目录存在
        self.documents_dir.mkdir(exist_ok=True)
        self.vectorstore_dir.mkdir(exist_ok=True)
        
        # 初始化文本分割器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
        
        # 初始化嵌入模型和向量存储
        self._initialize_embeddings()
        self._initialize_vector_store()
    
    def _initialize_embeddings(self):
        """初始化嵌入模型"""
        if self.embeddings is None:
            # 检查是否有可用的 CUDA
            device = "cuda" if torch.cuda.is_available() else "cpu"
            print(f"Using device: {device}")
            
            self.embeddings = HuggingFaceEmbeddings(
                model_name=self.embedding_model_name,
                model_kwargs={"device": device}
            )
            
            if device == "cuda":
                print(f"Using GPU: {torch.cuda.get_device_name(0)}")
                print(f"Available GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
    
    def _initialize_vector_store(self):
        """初始化向量存储"""
        if self.vector_store is None:
            # 如果向量存储目录存在，则加载现有的向量存储
            if any(self.vectorstore_dir.iterdir()):
                self.vector_store = Chroma(
                    persist_directory=str(self.vectorstore_dir),
                    embedding_function=self.embeddings
                )
            else:
                # 创建新的向量存储
                self.vector_store = Chroma(
                    persist_directory=str(self.vectorstore_dir),
                    embedding_function=self.embeddings
                )
    
    def load_documents(self) -> List[DocumentInfo]:
        """加载并处理文档目录中的所有文档"""
        try:
            # 使用DirectoryLoader加载文档
            loader = DirectoryLoader(
                str(self.documents_dir),
                glob="**/*.*",  # 匹配所有文件
                loader_cls=UnstructuredFileLoader,
                show_progress=True,
                use_multithreading=True
            )
            documents = loader.load()
            
            # 分割文档
            chunks = []
            for doc in documents:
                doc_chunks = self.text_splitter.split_documents([doc])
                chunks.extend(doc_chunks)
            
            # 将分割后的文档添加到向量存储
            if chunks:
                self.vector_store.add_documents(chunks)
                # 持久化向量存储
                self.vector_store.persist()
            
            # 返回处理后的文档信息
            return [
                DocumentInfo(
                    name=Path(doc.metadata.get("source", "unknown")).name,
                    path=str(doc.metadata.get("source", "")),
                    size=os.path.getsize(doc.metadata.get("source", "")),
                    modified=os.path.getmtime(doc.metadata.get("source", "")),
                    chunks=len(self.text_splitter.split_text(doc.page_content)),
                    status="processed"
                )
                for doc in documents
            ]
            
        except Exception as e:
            print(f"Error processing documents: {str(e)}")
            raise
    
    def search(self, query: str, k: int = 5) -> List[SearchResult]:
        """搜索相关文档"""
        if not self.vector_store:
            return []
        
        results = self.vector_store.similarity_search_with_score(query, k=k)
        return [
            SearchResult(
                content=doc.page_content,
                source=Path(doc.metadata.get("source", "unknown")).name,
                similarity=score
            )
            for doc, score in results
        ]
    
    def get_relevant_context(self, query: str, max_tokens: int = 2000) -> str:
        """获取与查询相关的上下文"""
        results = self.search(query)
        context = "\n\n".join([
            f"From {result.source}:\n{result.content}"
            for result in results
        ])
        return context[:max_tokens]  # 简单截断，实际应用中可能需要更智能的方式

    def delete_document(self, filename: str) -> bool:
        """删除文档及其向量"""
        try:
            file_path = self.documents_dir / filename
            if file_path.exists():
                # 删除文件
                file_path.unlink()
                
                # 重新初始化向量存储（简单方案，实际应用中可能需要更精细的控制）
                if self.vector_store:
                    self.vector_store = None
                    self._initialize_vector_store()
                
                return True
            return False
        except Exception as e:
            print(f"Error deleting document: {str(e)}")
            return False 