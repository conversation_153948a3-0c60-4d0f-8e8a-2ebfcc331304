"""
Error handling for the AI Assistant and MCP Marketplace
"""
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)

class AppError(Exception):
    """Base application error"""
    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class ConfigError(AppError):
    """Configuration error"""
    def __init__(self, message: str):
        super().__init__(message, status_code=500)

class APIError(AppError):
    """API error"""
    def __init__(self, message: str, status_code: int = 400):
        super().__init__(message, status_code=status_code)

class MCPError(AppError):
    """MCP-related error"""
    def __init__(self, message: str):
        super().__init__(message, status_code=500)

def setup_error_handlers(app: FastAPI):
    """
    Configure error handlers for the application
    
    Args:
        app: FastAPI application
    """
    @app.exception_handler(AppError)
    async def app_error_handler(request: Request, exc: AppError):
        logger.error(f"Application error: {exc.message}")
        return JSONResponse(
            status_code=exc.status_code,
            content={"error": exc.message}
        )
    
    @app.exception_handler(Exception)
    async def generic_error_handler(request: Request, exc: Exception):
        logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": "An unexpected error occurred"}
        ) 