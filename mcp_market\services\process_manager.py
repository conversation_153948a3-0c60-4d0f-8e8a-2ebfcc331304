"""
进程管理模块 - 管理MCP服务进程的启动、监控和停止
"""
import os
import sys
import subprocess
import logging
import json
import signal
import time
import threading
import platform
import shlex
import uuid
import socket
from typing import Dict, List, Optional, Tuple, Any, Union, Callable
from pathlib import Path
import psutil  # 需要安装: pip install psutil

from mcp_market.models.plugin import Plugin, PluginStatus
from mcp_market.core.dependency import DependencyManager

logger = logging.getLogger(__name__)


class ProcessInfo:
    """进程信息"""
    
    def __init__(self, process: subprocess.Popen, pid: int, port: int, plugin_id: str):
        """初始化进程信息
        
        Args:
            process: 进程对象
            pid: 进程ID
            port: 进程使用的端口
            plugin_id: 插件ID
        """
        self.process = process
        self.pid = pid
        self.port = port
        self.plugin_id = plugin_id
        self.start_time = time.time()
        self.monitoring = False
        self.monitor_thread = None
    
    def __str__(self):
        return f"ProcessInfo(pid={self.pid}, port={self.port}, plugin_id={self.plugin_id})"


class ProcessManager:
    """管理MCP服务进程的启动、监控和停止"""
    
    def __init__(self, logs_dir: str, base_port: int = 9000, 
                 on_process_exit: Optional[Callable[[str, int], None]] = None):
        """初始化进程管理器
        
        Args:
            logs_dir: 日志目录
            base_port: 端口基数，用于分配端口
            on_process_exit: 进程退出回调函数，参数为(plugin_id, exit_code)
        """
        self.logs_dir = Path(logs_dir)
        self.base_port = base_port
        self.on_process_exit = on_process_exit
        
        # 进程信息字典，键为插件ID
        self.processes: Dict[str, ProcessInfo] = {}
        
        # 正在使用的端口集合
        self.used_ports: set = set()
        
        # 用于同步访问的锁
        self._lock = threading.RLock()
        
        # 停止标志
        self._stop = False
        
        # 创建日志目录
        os.makedirs(self.logs_dir, exist_ok=True)
        
        logger.info(f"初始化进程管理器: 日志目录={self.logs_dir}, 端口基数={self.base_port}")
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(target=self._cleanup_processes, daemon=True)
        self._cleanup_thread.start()
    
    def start_process(self, plugin: Plugin, dependency_manager: DependencyManager,
                     entry_file: str, args: List[str] = None, 
                     env: Dict[str, str] = None, dependency_group = None) -> Tuple[bool, Optional[int], Optional[str]]:
        """启动插件进程
        
        Args:
            plugin: 插件对象
            dependency_manager: 依赖管理器
            entry_file: 入口文件路径
            args: 命令行参数
            env: 环境变量
            dependency_group: 依赖组对象，如果不提供则从plugin.dependency_group获取
        
        Returns:
            (成功标志, 端口, 错误信息)
        """
        with self._lock:
            plugin_id = plugin.id
            logger.info(f"启动插件进程: {plugin_id}")
            
            # 检查插件是否已经在运行
            if plugin_id in self.processes:
                logger.warning(f"插件 {plugin_id} 已经在运行")
                return True, self.processes[plugin_id].port, None
            
            # 获取Python解释器
            if not dependency_group and not plugin.dependency_group:
                return False, None, "插件未关联依赖组"
            
            # 使用传入的依赖组对象或尝试通过ID获取
            dep_group_to_use = dependency_group or plugin.dependency_group
            
            python_exec = dependency_manager.get_python_executable(dep_group_to_use)
            if not python_exec:
                return False, None, "无法获取Python解释器"
            
            # 分配端口
            port = self._allocate_port()
            if port is None:
                return False, None, "无法分配端口"
            
            # 准备日志文件
            log_file = self.logs_dir / f"{plugin_id}.log"
            
            # 准备环境变量
            process_env = os.environ.copy()
            if env:
                process_env.update(env)
            
            # 添加端口到环境变量
            process_env["MCP_PLUGIN_PORT"] = str(port)
            process_env["MCP_PLUGIN_ID"] = plugin_id
            
            # 构建命令
            cmd = [python_exec, entry_file]
            
            if args:
                cmd.extend(args)
            
            try:
                # 打开日志文件
                log_fd = open(log_file, "a", encoding="utf-8")
                
                # 启动进程
                logger.info(f"执行命令: {' '.join(cmd)}")
                
                # 创建进程
                process = subprocess.Popen(
                    cmd,
                    stdout=log_fd,
                    stderr=subprocess.STDOUT,
                    cwd=os.path.dirname(entry_file),
                    env=process_env,
                    start_new_session=True  # 创建新会话，避免接收父进程的信号
                )
                
                # 检查进程是否立即失败
                if process.poll() is not None:
                    exit_code = process.returncode
                    log_fd.close()
                    logger.error(f"插件进程启动失败，立即退出，退出代码: {exit_code}")
                    return False, None, f"进程启动失败，退出代码: {exit_code}"
                
                # 记录进程信息
                process_info = ProcessInfo(
                    process=process,
                    pid=process.pid,
                    port=port,
                    plugin_id=plugin_id
                )
                
                self.processes[plugin_id] = process_info
                self.used_ports.add(port)
                
                # 启动监控线程
                self._start_monitoring(process_info, log_fd)
                
                logger.info(f"插件进程启动成功: pid={process.pid}, port={port}")
                
                return True, port, None
                
            except Exception as e:
                logger.exception(f"启动插件进程时发生异常: {e}")
                
                # 释放端口
                if port in self.used_ports:
                    self.used_ports.remove(port)
                
                return False, None, str(e)
    
    def stop_process(self, plugin_id: str, force: bool = False) -> Tuple[bool, Optional[str]]:
        """停止插件进程
        
        Args:
            plugin_id: 插件ID
            force: 是否强制停止
        
        Returns:
            (成功标志, 错误信息)
        """
        with self._lock:
            if plugin_id not in self.processes:
                logger.warning(f"插件 {plugin_id} 未在运行")
                return True, None
            
            process_info = self.processes[plugin_id]
            logger.info(f"停止插件进程: {plugin_id}, pid={process_info.pid}")
            
            try:
                # 获取进程对象
                process = psutil.Process(process_info.pid)
                
                # 首先尝试优雅地终止进程
                if not force:
                    # 在Windows上使用CTRL_BREAK_EVENT，在Unix上使用SIGTERM
                    if platform.system() == "Windows":
                        process.send_signal(signal.CTRL_BREAK_EVENT)
                    else:
                        process.send_signal(signal.SIGTERM)
                    
                    # 等待进程退出
                    try:
                        process_info.process.wait(timeout=5)
                        # 进程已退出
                        logger.info(f"插件进程已优雅退出: {plugin_id}")
                        return True, None
                    except subprocess.TimeoutExpired:
                        # 进程未在超时内退出
                        logger.warning(f"插件进程未在超时内退出，将强制终止: {plugin_id}")
                        # 继续执行强制终止
                
                # 强制终止进程及其子进程
                self._kill_process_tree(process_info.pid)
                
                # 等待进程退出
                try:
                    process_info.process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    # 如果还未退出，再次尝试杀死
                    try:
                        process.kill()
                    except psutil.NoSuchProcess:
                        pass
                
                logger.info(f"插件进程已强制终止: {plugin_id}")
                
                # 释放端口
                if process_info.port in self.used_ports:
                    self.used_ports.remove(process_info.port)
                
                # 移除进程信息
                del self.processes[plugin_id]
                
                return True, None
                
            except psutil.NoSuchProcess:
                logger.warning(f"插件进程不存在: {plugin_id}, pid={process_info.pid}")
                
                # 清理资源
                if process_info.port in self.used_ports:
                    self.used_ports.remove(process_info.port)
                
                del self.processes[plugin_id]
                
                return True, None
                
            except Exception as e:
                logger.exception(f"停止插件进程时发生异常: {e}")
                return False, str(e)
    
    def is_running(self, plugin_id: str) -> bool:
        """检查插件是否在运行
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            是否在运行
        """
        with self._lock:
            if plugin_id not in self.processes:
                return False
            
            process_info = self.processes[plugin_id]
            
            # 检查进程是否仍在运行
            try:
                process = psutil.Process(process_info.pid)
                return process.is_running() and process.status() != psutil.STATUS_ZOMBIE
            except psutil.NoSuchProcess:
                # 进程已不存在，清理资源
                if process_info.port in self.used_ports:
                    self.used_ports.remove(process_info.port)
                
                del self.processes[plugin_id]
                
                return False
            except Exception as e:
                logger.exception(f"检查插件进程状态时发生异常: {e}")
                return False
    
    def get_process_info(self, plugin_id: str) -> Optional[Dict[str, Any]]:
        """获取进程信息
        
        Args:
            plugin_id: 插件ID
        
        Returns:
            进程信息
        """
        with self._lock:
            if plugin_id not in self.processes:
                return None
            
            process_info = self.processes[plugin_id]
            
            try:
                process = psutil.Process(process_info.pid)
                
                # 获取进程信息
                cpu_percent = process.cpu_percent(interval=0.1)
                memory_info = process.memory_info()
                create_time = process.create_time()
                
                # 计算运行时间
                running_time = time.time() - create_time
                
                return {
                    "pid": process_info.pid,
                    "port": process_info.port,
                    "cpu_percent": cpu_percent,
                    "memory_rss": memory_info.rss,
                    "memory_vms": memory_info.vms,
                    "create_time": create_time,
                    "running_time": running_time,
                    "status": process.status()
                }
                
            except psutil.NoSuchProcess:
                return {
                    "pid": process_info.pid,
                    "port": process_info.port,
                    "status": "terminated"
                }
                
            except Exception as e:
                logger.exception(f"获取插件进程信息时发生异常: {e}")
                return {
                    "pid": process_info.pid,
                    "port": process_info.port,
                    "status": "unknown",
                    "error": str(e)
                }
    
    def get_all_running_processes(self) -> Dict[str, Dict[str, Any]]:
        """获取所有运行中的进程信息
        
        Returns:
            插件ID到进程信息的映射
        """
        with self._lock:
            result = {}
            
            for plugin_id in list(self.processes.keys()):
                info = self.get_process_info(plugin_id)
                if info:
                    result[plugin_id] = info
            
            return result
    
    def shutdown(self):
        """关闭进程管理器，停止所有进程"""
        logger.info("关闭进程管理器")
        
        # 设置停止标志
        self._stop = True
        
        # 停止所有进程
        with self._lock:
            for plugin_id in list(self.processes.keys()):
                try:
                    self.stop_process(plugin_id, force=True)
                except Exception as e:
                    logger.exception(f"关闭插件进程时发生异常: {e}")
        
        # 等待清理线程退出
        if self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5)
    
    def _allocate_port(self) -> Optional[int]:
        """分配一个可用端口
        
        Returns:
            端口号
        """
        # 从基础端口开始，寻找一个未被使用的端口
        for port in range(self.base_port, self.base_port + 1000):
            if port not in self.used_ports and self._is_port_available(port):
                return port
        
        logger.error("无法分配端口，所有端口均已被占用")
        return None
    
    def _is_port_available(self, port: int) -> bool:
        """检查端口是否可用
        
        Args:
            port: 端口号
        
        Returns:
            是否可用
        """
        try:
            # 尝试绑定端口
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("localhost", port))
                return True
        except:
            return False
    
    def _start_monitoring(self, process_info: ProcessInfo, log_fd):
        """启动进程监控
        
        Args:
            process_info: 进程信息
            log_fd: 日志文件描述符
        """
        if process_info.monitoring:
            return
        
        process_info.monitoring = True
        
        def monitor_process():
            """监控进程"""
            plugin_id = process_info.plugin_id
            
            logger.debug(f"开始监控插件进程: {plugin_id}, pid={process_info.pid}")
            
            # 等待进程退出
            exit_code = process_info.process.wait()
            
            logger.info(f"插件进程已退出: {plugin_id}, pid={process_info.pid}, exit_code={exit_code}")
            
            # 关闭日志文件
            log_fd.close()
            
            # 清理资源
            with self._lock:
                if plugin_id in self.processes:
                    if process_info.port in self.used_ports:
                        self.used_ports.remove(process_info.port)
                    
                    del self.processes[plugin_id]
            
            # 调用回调函数
            if self.on_process_exit:
                try:
                    self.on_process_exit(plugin_id, exit_code)
                except Exception as e:
                    logger.exception(f"进程退出回调函数执行时发生异常: {e}")
        
        # 创建监控线程
        process_info.monitor_thread = threading.Thread(
            target=monitor_process,
            daemon=True
        )
        
        # 启动线程
        process_info.monitor_thread.start()
    
    def _kill_process_tree(self, pid: int):
        """杀死进程树
        
        Args:
            pid: 进程ID
        """
        try:
            parent = psutil.Process(pid)
            
            # 获取所有子进程
            children = parent.children(recursive=True)
            
            # 杀死所有子进程
            for child in children:
                try:
                    child.kill()
                except psutil.NoSuchProcess:
                    pass
            
            # 杀死父进程
            parent.kill()
            
        except psutil.NoSuchProcess:
            pass
        except Exception as e:
            logger.exception(f"杀死进程树时发生异常: {e}")
    
    def _cleanup_processes(self):
        """清理进程，周期性检查进程状态"""
        logger.info("启动进程清理线程")
        
        while not self._stop:
            try:
                # 获取所有插件ID的副本
                with self._lock:
                    plugin_ids = list(self.processes.keys())
                
                # 检查每个进程
                for plugin_id in plugin_ids:
                    try:
                        # 检查进程是否仍在运行
                        self.is_running(plugin_id)
                    except Exception as e:
                        logger.exception(f"检查进程状态时发生异常: {e}")
            
            except Exception as e:
                logger.exception(f"进程清理线程发生异常: {e}")
            
            # 每5秒检查一次
            time.sleep(5) 