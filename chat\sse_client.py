"""
Simple SSE client for MCP servers
"""
import time
import requests
import json
from typing import Dict, Any, Optional

class SSEMCPClient:
    """Client for Server-Sent Events (SSE) MCP endpoints"""
    
    def __init__(self, url: str, timeout: int = 30):
        """Initialize with SSE endpoint URL"""
        self.url = url
        self.timeout = timeout
        
    def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the SSE endpoint"""
        start_time = time.time()
        
        try:
            # Use custom headers for SSE connection
            headers = {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            }
            
            # Try to connect to the SSE endpoint
            response = requests.get(
                self.url, 
                headers=headers, 
                stream=True, 
                timeout=self.timeout
            )
            
            response_time = round((time.time() - start_time) * 1000, 1)
            
            # Check if successful
            if response.status_code == 200 and 'text/event-stream' in response.headers.get('content-type', ''):
                return {
                    'status': 'healthy',
                    'message': 'SSE connection successful',
                    'response_time': response_time,
                    'content_type': response.headers.get('content-type', ''),
                    'server': response.headers.get('server', '')
                }
            elif response.status_code == 200:
                return {
                    'status': 'warning',
                    'message': f'Wrong content type: {response.headers.get("content-type", "none")}',
                    'response_time': response_time
                }
            else:
                return {
                    'status': 'error',
                    'message': f'HTTP error: {response.status_code}',
                    'response_time': response_time
                }
        except requests.exceptions.Timeout:
            return {
                'status': 'error',
                'message': 'Connection timeout',
                'response_time': None
            }
        except requests.exceptions.RequestException as e:
            return {
                'status': 'error',
                'message': f'Connection error: {str(e)}',
                'response_time': None
            }
    
    def send_message(self, tool: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Send a message to the MCP server"""
        # Convert SSE URL to message URL
        if self.url.endswith('/sse'):
            base_url = self.url[:-4]
        else:
            base_url = self.url
            
        message_url = f"{base_url}/message"
        
        # Send request
        try:
            response = requests.post(
                message_url,
                json={'tool': tool, 'params': params},
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'error': f'HTTP error: {response.status_code}',
                    'message': f'Failed to call tool {tool}'
                }
        except Exception as e:
            return {
                'error': str(e),
                'message': f'Failed to call tool {tool}'
            } 