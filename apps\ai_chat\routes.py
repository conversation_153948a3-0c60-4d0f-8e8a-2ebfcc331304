"""
Routes for AI Chat application
"""
import os
import logging
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates

# Import services
from .services import ChatService

logger = logging.getLogger(__name__)

# Setup templates
templates_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "templates")
templates = Jinja2Templates(directory=templates_dir)

def setup_routes(app: FastAPI):
    """
    Setup routes for the AI Chat application

    Args:
        app: FastAPI application
    """
    # Initialize services
    chat_service = ChatService(storage_dir="chat_data")

    # Main page route
    @app.get("/", response_class=HTMLResponse)
    async def index(request: Request):
        """
        Main page for AI Chat
        """
        return templates.TemplateResponse("index.html", {"request": request})