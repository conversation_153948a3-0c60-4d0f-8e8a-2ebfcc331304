"""
Security utility functions
"""
import os
import logging
import hashlib
import secrets
import base64
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

def generate_random_string(length: int = 32) -> str:
    """
    Generate a random string
    
    Args:
        length: Length of the random string
        
    Returns:
        Random string
    """
    return secrets.token_hex(length // 2)

def hash_password(password: str) -> str:
    """
    Hash a password using SHA-256
    
    Args:
        password: Password to hash
        
    Returns:
        Hashed password
    """
    return hashlib.sha256(password.encode("utf-8")).hexdigest()

def verify_password(password: str, hashed_password: str) -> bool:
    """
    Verify a password against a hash
    
    Args:
        password: Password to verify
        hashed_password: Hashed password to verify against
        
    Returns:
        True if password matches hash, False otherwise
    """
    return hash_password(password) == hashed_password

def encode_api_key(api_key: str) -> str:
    """
    Encode an API key for storage
    
    Args:
        api_key: API key to encode
        
    Returns:
        Encoded API key
    """
    return base64.b64encode(api_key.encode("utf-8")).decode("utf-8")

def decode_api_key(encoded_api_key: str) -> str:
    """
    Decode an API key for use
    
    Args:
        encoded_api_key: Encoded API key
        
    Returns:
        Decoded API key
    """
    return base64.b64decode(encoded_api_key.encode("utf-8")).decode("utf-8")

def sanitize_input(input_string: str) -> str:
    """
    Sanitize user input to prevent injection attacks
    
    Args:
        input_string: Input string to sanitize
        
    Returns:
        Sanitized input string
    """
    # Simple sanitization, should be replaced with a proper library
    # Remove HTML tags
    sanitized = input_string.replace("<", "&lt;").replace(">", "&gt;")
    # Remove script tags
    sanitized = sanitized.replace("javascript:", "")
    return sanitized

def validate_server_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and sanitize server configuration
    
    Args:
        config: Server configuration
        
    Returns:
        Validated server configuration
    """
    validated = {}
    
    # Validate required fields
    if "name" not in config or not config["name"]:
        raise ValueError("Server name is required")
    validated["name"] = sanitize_input(config["name"])
    
    # Validate and sanitize other fields
    if "description" in config and config["description"]:
        validated["description"] = sanitize_input(config["description"])
    
    if "connection_type" in config:
        connection_type = config["connection_type"]
        if connection_type not in ["stdio", "sse", "streamableHttp"]:
            raise ValueError(f"Invalid connection type: {connection_type}")
        validated["connection_type"] = connection_type
    
    # Copy other fields
    for key in ["command", "args", "env", "cwd", "url", "headers", "timeout", "retry_count"]:
        if key in config and config[key] is not None:
            validated[key] = config[key]
    
    return validated 