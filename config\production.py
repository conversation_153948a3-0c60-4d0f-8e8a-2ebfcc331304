"""
Production configuration for the AI Assistant and MCP Marketplace
"""
from .default import DEFAULT_CONFIG

# Deep copy the default config to avoid modifying it
import copy
PROD_CONFIG = copy.deepcopy(DEFAULT_CONFIG)

# Override production-specific settings
PROD_CONFIG.update({
    "app": {
        "host": "0.0.0.0",  # Listen on all interfaces in production
        "port": 8081,
        "debug": False,
        "reload": False,
    }
}) 