"""
Main application entry point for AI Assistant and MCP Marketplace
"""
import logging
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse, RedirectResponse

# Import core modules
from core.app import create_app
from core.errors import setup_error_handlers
from core.logger import setup_logger

# Import applications
from apps.ai_chat.app import create_ai_chat_app
from apps.mcp_market.app import create_mcp_market_app

# Import API routes
from api.routes import setup_api_routes

# Setup logging
logger = setup_logger("app", "app.log")

# Create main application
app, templates = create_app()

# Setup error handlers
setup_error_handlers(app)

# Add AI Chat sub-application
ai_chat_app = create_ai_chat_app()
app.mount("/ai-chat", ai_chat_app, name="ai_chat")

# Add MCP Marketplace sub-application
mcp_market_app = create_mcp_market_app()
app.mount("/mcp-market", mcp_market_app, name="mcp_market")

# Setup API routes
setup_api_routes(app)

# Main index route
@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Main index page"""
    return templates.TemplateResponse("index.html", {"request": request})

# Redirect /chat to /ai-chat for compatibility
@app.get("/chat")
async def redirect_to_chat():
    """Redirect to AI chat for compatibility with old URLs"""
    return RedirectResponse(url="/ai-chat")

if __name__ == "__main__":
    import uvicorn
    
    # Import configuration
    from config import config
    
    # Run the server
    uvicorn.run(
        "app:app",
        host=config["app"]["host"],
        port=config["app"]["port"],
        reload=config["app"]["reload"]
    )
